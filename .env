# Flutter APK Build & Telegram Upload Service Configuration
# Copy this file to .env and configure your settings

# =============================================================================
# TELEGRAM CONFIGURATION
# =============================================================================

# Telegram Bot Token (Required)
# Get this from @BotFather on Telegram
TELEGRAM_BOT_TOKEN=**********************************************

# Telegram Chat ID (Required)
# This can be a channel ID (e.g., @your_channel) or group ID (e.g., -1001234567890)
# Use @userinfobot to get chat IDs
TELEGRAM_CHAT_ID=-1002063224194

# Telegram Topic ID (Optional)
# Only needed for groups with topics enabled
# Leave empty for channels or regular groups
TELEGRAM_TOPIC_ID=918

# =============================================================================
# BUILD CONFIGURATION
# =============================================================================

# Build Type (debug or release)
# Default: release
BUILD_TYPE=release

# Build Flavor (Optional)
# Specify if your app has flavors configured
# Examples: dev, staging, production
BUILD_FLAVOR=

# App Name Override (Optional)
# Override the app name from pubspec.yaml
# Useful for different environments
APP_NAME_OVERRIDE=

# =============================================================================
# GIT CONFIGURATION
# =============================================================================

# Git Branch Override (Optional)
# Override the current git branch name in upload message
# Useful for CI/CD environments
GIT_BRANCH_OVERRIDE=

# =============================================================================
# UPLOAD CONFIGURATION
# =============================================================================

# Upload Timeout (seconds)
# How long to wait for Telegram upload to complete
# Default: 300 (5 minutes)
UPLOAD_TIMEOUT=300

# Maximum File Size (bytes)
# Telegram bot API limit is 50MB (52428800 bytes)
# Default: 50000000 (approximately 50MB)
MAX_FILE_SIZE=50000000

# =============================================================================
# FLUTTER CONFIGURATION
# =============================================================================

# Flutter Root Path (Optional)
# Specify if Flutter is not in your PATH
# Example: /Users/<USER>/flutter
FLUTTER_ROOT=

# Flutter Channel (Optional)
# Specify Flutter channel to use
# Options: stable, beta, dev, master
FLUTTER_CHANNEL=

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Debug Mode (Optional)
# Enable verbose logging
# Set to 1 to enable, 0 or empty to disable
DEBUG=0

# Clean Build (Optional)
# Always clean before building
# Set to 1 to enable, 0 or empty to disable
ALWAYS_CLEAN=0

# Run Tests (Optional)
# Always run tests before building
# Set to 1 to enable, 0 or empty to disable
ALWAYS_TEST=0

# Custom Build Arguments (Optional)
# Additional arguments to pass to flutter build
# Example: --dart-define=API_URL=https://api.example.com
CUSTOM_BUILD_ARGS=

# =============================================================================
# CHANGELOG CONFIGURATION
# =============================================================================

# Changelog File Path (Optional)
# Path to changelog file relative to project root
# Default: CHANGELOG.md
CHANGELOG_FILE=CHANGELOG.md

# Changelog Lines (Optional)
# Number of changelog lines to include in upload message
# Default: 5
CHANGELOG_LINES=5

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# Success Notification (Optional)
# Additional chat ID to notify on successful upload
SUCCESS_NOTIFICATION_CHAT_ID=

# Error Notification (Optional)
# Additional chat ID to notify on build/upload errors
ERROR_NOTIFICATION_CHAT_ID=

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# Example for development environment:
# BUILD_TYPE=debug
# BUILD_FLAVOR=dev
# APP_NAME_OVERRIDE=MyApp Dev
# TELEGRAM_CHAT_ID=@dev_channel

# Example for production environment:
# BUILD_TYPE=release
# BUILD_FLAVOR=production
# APP_NAME_OVERRIDE=MyApp
# TELEGRAM_CHAT_ID=@production_channel

# Example for CI/CD:
# GIT_BRANCH_OVERRIDE=${CI_COMMIT_REF_NAME}
# DEBUG=1
# ALWAYS_CLEAN=1