// import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get_storage/get_storage.dart';
import 'package:click_bazaar/core/function/functions.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/core/widgets/custom_toast.dart';
import 'package:click_bazaar/features/face_control/functional_lock_page.dart';
// import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:click_bazaar/core/services/navigation_service.dart';
import 'package:flutter/material.dart';

abstract class LocationService {
  Future<String> getLatLang();
}

class LocationServiceImpl extends LocationService {
  @override
  Future<String> getLatLang() async {
    LocationPermission permission;
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.deniedForever) {
        return "0,0";
      }
    }
    final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high);

    return "${position.latitude},${position.longitude}";
  }
}

Future<Position> determinePosition([bool? isBackgroundService]) async {
  bool serviceEnabled;
  LocationPermission permission;
  var sm = SessionManager();
  final storage = GetStorage();
  serviceEnabled = await Geolocator.isLocationServiceEnabled();

  var isLive = await sm.get(functional_live) ?? false;

  var backService = isBackgroundService ?? false;
  if (!serviceEnabled) {
    storage.write(is_gps_active, false);

    if (!backService) {
      if (!isLive) {
        final navigator = NavigationService.navigatorKey.currentState;
        if (navigator != null) {
          navigator.pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const FunctionalLockPage()),
            (route) => false,
          );
        }
      }
    }
    if (backService) {
      createNotification(await turnLocationOn(storage));
    } else {
      CustomToast.showToast("Turn on location");
    }

    return Future.error("LOCATION_OFF");
  }

  permission = await Geolocator.checkPermission();

  if (permission == LocationPermission.denied) {
    permission = await Geolocator.requestPermission();
    if (backService) {
      createNotification("Give location permission");
    } else {
      CustomToast.showToast("Give location permission");
    }

    if (permission == LocationPermission.denied) {
      storage.write(is_gps_active, false);
      if (!backService) {
        if (!isLive) {
          final navigator = NavigationService.navigatorKey.currentState;
          if (navigator != null) {
            navigator.pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const FunctionalLockPage()),
              (route) => false,
            );
          }
        }
      }

      return Future.error("DENIED");
    }
  }

  if (permission == LocationPermission.deniedForever) {
    storage.write(is_gps_active, false);
    if (!backService) {
      if (!isLive) {
        final navigator = NavigationService.navigatorKey.currentState;
        if (navigator != null) {
          navigator.pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const FunctionalLockPage()),
            (route) => false,
          );
        }
      }
    }

    if (backService) {
      createNotification(await giveLocationPermission(storage));
    } else {
      CustomToast.showToast("Give location permission");
    }
    return Future.error("DENIED_FOREVER");
  }

  try {
    var location = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.bestForNavigation);

    if (location.isMocked) {
      storage.write(is_not_mocked, false);
      if (!backService) {
        if (!isLive) {
          final navigator = NavigationService.navigatorKey.currentState;
          if (navigator != null) {
            navigator.pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const FunctionalLockPage()),
              (route) => false,
            );
          }
        }
      }

      if (backService) {
        createNotification(await fraudLocation());
      } else {
        CustomToast.showToast("Fraud location");
      }

      return Future.error('MOCKED',
          StackTrace.fromString('${location.latitude},${location.longitude}'));
    } else {
      await storage.write(is_gps_active, true);
      await storage.write(is_not_mocked, true);
      return location;
    }
  } on LocationServiceDisabledException catch (e) {
    return Future.error("LOCATION_OFF");
  } catch (e) {
    return Future.error("LOCATION_OFF");
  }
}

Future<String> turnLocationOn(GetStorage storage) async {
  String? lang = storage.read(language_pref);
  if (lang == 'uz') {
    return "Iltimos, ilova tog'ri ishlashi uchun lokatsiyani yoqing!";
  } else if (lang == 'ru') {
    return "Пожалуйста, включите местоположение, чтобы приложение работало правильно!";
  } else {
    return "Ótinish, qosımsha tog'ri islewi ushın lokatsiyani qosıń!";
  }
}

Future<String> giveLocationPermission(GetStorage storage) async {
  String? lang = storage.read(language_pref);
  if (lang == 'uz') {
    return "Lokatsiyaga ruxsat bering";
  } else if (lang == 'ru') {
    return "Разрешить местоположение";
  } else {
    return "Lokatsiyaga ruxsat beriń";
  }
}

Future<String> fraudLocation() async {
  final storage = GetStorage();
  String? lang = storage.read(language_pref);
  if (lang == 'uz') {
    return "Lokatsiyada aldov aniqlandi!";
  } else if (lang == 'ru') {
    return "Обнаружено мошенничество с местоположением!";
  } else {
    return "Обнаружено мошенничество с местоположением!";
  }
}
