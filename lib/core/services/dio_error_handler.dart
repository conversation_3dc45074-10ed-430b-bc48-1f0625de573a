import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../utils/app_functions.dart';

/// Universal Dio error handling service
/// Provides consistent error handling and user-friendly messages across the app
class DioErrorHandler {
  /// Handle Dio exceptions and return appropriate error messages
  static String handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Ulanish vaqti tugadi. Iltimos, qayta urinib ko\'ring.';
      
      case DioExceptionType.sendTimeout:
        return 'Ma\'lumot yuborish vaqti tugadi. Iltimos, qayta urinib ko\'ring.';
      
      case DioExceptionType.receiveTimeout:
        return 'Ma\'lumot olish vaqti tugadi. Iltimos, qayta urinib ko\'ring.';
      
      case DioExceptionType.badResponse:
        return _handleBadResponse(error);
      
      case DioExceptionType.cancel:
        return 'So\'rov bekor qilindi.';
      
      case DioExceptionType.connectionError:
        return 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.';
      
      case DioExceptionType.badCertificate:
        return 'Xavfsizlik sertifikati xatoligi.';
      
      case DioExceptionType.unknown:
      default:
        return 'Kutilmagan xatolik yuz berdi. Iltimos, qayta urinib ko\'ring.';
    }
  }

  /// Handle bad response errors based on status code
  static String _handleBadResponse(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    // Try to extract error message from response
    String? serverMessage;
    if (responseData is Map<String, dynamic>) {
      serverMessage = responseData['message'] ?? 
                     responseData['error'] ?? 
                     responseData['detail'];
    }

    switch (statusCode) {
      case 400:
        return serverMessage ?? 'Noto\'g\'ri so\'rov. Ma\'lumotlarni tekshiring.';
      
      case 401:
        return serverMessage ?? 'Avtorizatsiya xatoligi. Qayta kiring.';
      
      case 403:
        return serverMessage ?? 'Ruxsat yo\'q. Sizda bu amalni bajarish huquqi yo\'q.';
      
      case 404:
        return serverMessage ?? 'Ma\'lumot topilmadi.';
      
      case 409:
        return serverMessage ?? 'Ma\'lumotlar ziddiyati.';
      
      case 422:
        return serverMessage ?? 'Ma\'lumotlar formati noto\'g\'ri.';
      
      case 429:
        return serverMessage ?? 'Juda ko\'p so\'rov. Biroz kuting.';
      
      case 500:
        return serverMessage ?? 'Server xatoligi. Iltimos, keyinroq urinib ko\'ring.';
      
      case 502:
        return serverMessage ?? 'Server javob bermayapti.';
      
      case 503:
        return serverMessage ?? 'Xizmat vaqtincha mavjud emas.';
      
      default:
        return serverMessage ?? 'Server xatoligi ($statusCode).';
    }
  }

  /// Handle any exception and return appropriate error message
  static String handleGenericError(dynamic error) {
    if (error is DioException) {
      return handleDioError(error);
    } else {
      return 'Kutilmagan xatolik: ${error.toString()}';
    }
  }

  /// Show error message as toast and return the message
  static String handleErrorWithToast(dynamic error, {required BuildContext context}) {
    final message = handleGenericError(error);
    AppFunctions.showErrorSnackBar(context, message);
    return message;
  }

  /// Show error message as snackbar (requires context)
  static void showErrorSnackBar(dynamic error, BuildContext context) {
    final message = handleGenericError(error);
    AppFunctions.showErrorSnackBar(context, message);
  }

  /// Extract validation errors from 422 response
  static Map<String, List<String>> extractValidationErrors(DioException error) {
    final Map<String, List<String>> validationErrors = {};
    
    if (error.response?.statusCode == 422 && 
        error.response?.data is Map<String, dynamic>) {
      
      final data = error.response!.data as Map<String, dynamic>;
      
      // Handle Laravel-style validation errors
      if (data.containsKey('errors')) {
        final errors = data['errors'] as Map<String, dynamic>;
        errors.forEach((field, messages) {
          if (messages is List) {
            validationErrors[field] = messages.cast<String>();
          } else if (messages is String) {
            validationErrors[field] = [messages];
          }
        });
      }
      
      // Handle other validation error formats
      else if (data.containsKey('details')) {
        final details = data['details'];
        if (details is Map<String, dynamic>) {
          details.forEach((field, messages) {
            if (messages is List) {
              validationErrors[field] = messages.cast<String>();
            } else if (messages is String) {
              validationErrors[field] = [messages];
            }
          });
        }
      }
    }
    
    return validationErrors;
  }

  /// Check if error is a network connectivity issue
  static bool isNetworkError(dynamic error) {
    if (error is DioException) {
      return error.type == DioExceptionType.connectionError ||
             error.type == DioExceptionType.connectionTimeout ||
             error.type == DioExceptionType.receiveTimeout ||
             error.type == DioExceptionType.sendTimeout;
    }
    return false;
  }

  /// Check if error requires user re-authentication
  static bool requiresReauth(dynamic error) {
    if (error is DioException) {
      return error.response?.statusCode == 401;
    }
    return false;
  }

  /// Get error code from DioException
  static String? getErrorCode(DioException error) {
    final responseData = error.response?.data;
    if (responseData is Map<String, dynamic>) {
      return responseData['code']?.toString() ?? 
             responseData['error_code']?.toString();
    }
    return null;
  }
}
