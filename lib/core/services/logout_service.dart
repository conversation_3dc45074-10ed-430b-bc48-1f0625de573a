import 'package:click_bazaar/core/theme/theme_manager.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import '../utils/app_constants.dart';
import 'package:path/path.dart';

/// Universal logout service for handling user logout across the app
class LogoutService {
  final GetStorage _storage;
  final SharedPreferences prefs;

  LogoutService( {required GetStorage storage, required this.prefs}) : _storage = storage;

  /// Perform complete logout by clearing all stored data
  Future<void> performLogout({
    bool clearCache = true,
    bool showMessage = false,
    BuildContext? context,
  }) async {

    final themeManager = ThemeManager();
    try {

      //Remove face data
      String databasesPath = await getDatabasesPath();
      String path = join(databasesPath, 'person.db');

      await deleteDatabase(path); // just for testing

      print('🚪 Starting logout process...');
      
      // Clear authentication tokens
      await _storage.remove(TOKEN);
      await _storage.remove(REFRESH_TOKEN);
      
      // Clear user data
      await _storage.remove(USER_ROLE);
      await _storage.remove(USER_ID);
      await _storage.remove(USER_PROFILE);
      await _storage.remove(MARKET_ID);
      themeManager.clearTheme();

      // Clear cached data if requested
      if (clearCache) {
        await _clearCachedData();
      }
      
      // Clear demo mode
      await _storage.remove(is_demo);
      
      print('✅ Logout completed successfully');
      
      // Show message if requested
      if (showMessage && context != null) {
        _showLogoutMessage(context);
      }
      
    } catch (e) {
      print('❌ Error during logout: $e');
      // Even if there's an error, try to clear critical data
      await _storage.remove(TOKEN);
      await _storage.remove(REFRESH_TOKEN);
    }
  }

  /// Clear all cached data
  Future<void> _clearCachedData() async {
    try {
      // Clear profile cache
      await _storage.remove('cached_user_profile');
      await _storage.remove('profile_cache_time');

      // Clear seller profile cache keys
      await _storage.remove('sot_user_profile');
      await _storage.remove('sot_profile_cache_time');

      // Add other cached data keys here as needed
      print('🗑️ Cached data cleared');
    } catch (e) {
      print('⚠️ Error clearing cached data: $e');
    }
  }

  /// Completely erase all GetStorage data (nuclear option)
  Future<void> eraseAllStorage() async {
    try {
      print('🧹 Erasing all GetStorage data...');
      await _storage.erase();
      print('✅ All GetStorage data erased successfully');
    } catch (e) {
      print('❌ Error erasing GetStorage: $e');
      // Fallback: try to clear known keys individually
      await _clearKnownKeys();
    }
  }

  /// Clear all known storage keys individually (fallback method)
  Future<void> _clearKnownKeys() async {
    try {
      final knownKeys = [
        TOKEN,
        REFRESH_TOKEN,
        USER_ROLE,
        USER_ID,
        USER_PROFILE,
        MARKET_ID,
        is_demo,
        'cached_user_profile',
        'profile_cache_time',
        'sot_user_profile',
        'sot_profile_cache_time',
        APP_VERSION,
      ];

      for (final key in knownKeys) {
        await _storage.remove(key);
      }
      print('🗑️ Known keys cleared as fallback');
    } catch (e) {
      print('⚠️ Error clearing known keys: $e');
    }
  }

  /// Show logout message to user
  void _showLogoutMessage(BuildContext context) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Hisobdan chiqildi'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Check if user is currently logged in
  bool isLoggedIn() {
    final token = _storage.read(TOKEN);
    return token != null && token.toString().isNotEmpty;
  }

  /// Get current user role
  String? getCurrentUserRole() {
    return _storage.read(USER_ROLE);
  }

  /// Get current user ID
  String? getCurrentUserId() {
    return _storage.read(USER_ID);
  }

  /// Clear only authentication tokens (keep other data)
  Future<void> clearTokensOnly() async {
    await _storage.remove(TOKEN);
    await _storage.remove(REFRESH_TOKEN);
    print('🔑 Authentication tokens cleared');
  }

  /// Logout and navigate to login page
  Future<void> logoutAndNavigateToLogin(BuildContext context) async {
    await performLogout(clearCache: true, showMessage: true, context: context);

    if (context.mounted) {
      // Navigate to login page and clear navigation stack
      Navigator.of(context).pushNamedAndRemoveUntil(
        '/login',
        (route) => false,
      );
    }
  }

  /// Complete logout with full storage erasure (most comprehensive)
  Future<void> performCompleteLogout({
    bool showMessage = false,
    BuildContext? context,
  }) async {
    try {
      print('🚪 Starting complete logout with storage erasure...');

      // Erase all GetStorage data
      await eraseAllStorage();

      print('✅ Complete logout finished successfully');

      // Show message if requested
      if (showMessage && context != null) {
        _showLogoutMessage(context);
      }

    } catch (e) {
      print('❌ Error during complete logout: $e');
      // Fallback to regular logout
      await performLogout(clearCache: true, showMessage: false, context: context);
    }
  }
}
