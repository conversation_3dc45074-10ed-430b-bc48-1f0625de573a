import 'package:flutter/material.dart';

/// App color constants
/// This class contains all the color definitions used throughout the app
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // Primary Colors
  static const Color cFirstColor = Color(0xFF0065FF);
  static const Color cSecondaryColor = Color(0xFF5095FF);
  static const Color cGreenishColor = Color(0xFF039855);
  static const Color cReddishColor = Color(0xFFFF0053);
  static const Color cCarrotColor = Color(0xFFE76B0F);
  static const Color cUnbindedColor = Color(0xFF3A3B3C); // Swapped: now gray for unbinded status (was border color)
  static const Color cYellowishColor = Color(0xFFFFB950); // Swapped: now yellow for empty/available status (was unbinded color)

  ///Gray-ish colors for beautiful UI (Dark Theme)
  static const Color cBackgroundColor = Color(0xFF141414);
  static const Color cCardsColor = Color(0xFF232425);
  static const Color cTextGrayColor = Color(0xFF929699);
  static const Color cGrayBorderColor = Color(0xFF454648);
  static const Color cGrayForegroundColor = Color(0xFF313334);
  static const Color cGrayDarkColor = Color(0xFF5D5D5D);

  ///Light theme colors
  static const Color cLightBackgroundColor = Color(0xFFF8F9FA);
  static const Color cLightCardsColor = Color(0xFFFFFFFF);
  static const Color cLightTextGrayColor = Color(0xFF6C757D);
  static const Color cLightBorderColor = Color(0xFFE9ECEF);
  static const Color cLightForegroundColor = Color(0xFFF1F3F4);
  static const Color cLightSurfaceColor = Color(0xFFFDFDFD);

  // Additional colors for better theming
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color transparent = Colors.transparent;

  // Gradient colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [cFirstColor, cGreenishColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cardGradient = LinearGradient(
    colors: [cGrayForegroundColor, cCardsColor],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // Status colors
  static const Color success = cGreenishColor;
  static const Color error = cReddishColor;
  static const Color warning = cCarrotColor;
  static const Color info = cFirstColor;

  // Text colors
  static const Color primaryText = white;
  static const Color secondaryText = cTextGrayColor;
  static const Color disabledText = Color(0xFF5A5A5A);

  // Surface colors
  static const Color surface = cCardsColor;
  static const Color surfaceVariant = Color(0xFF2A2B2C);
  static const Color background = cBackgroundColor;
  static const Color backgroundVariant = Color(0xFF1A1A1A);

  // Border colors
  static const Color border = Color(0xFF3A3B3C);
  static const Color borderLight = Color(0xFF4A4B4C);
  static const Color borderDark = Color(0xFF2A2B2C);

  // Shadow colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x26000000);
  
  // Beautiful shadow colors for primary components
  static const Color primaryShadowDark = Color(0x330065FF);
  static const Color primaryShadowLight = Color(0x1A0065FF);
  static const Color cardShadowDark = Color(0x40000000);
  static const Color cardShadowLight = Color(0x08000000);

  // Overlay colors
  static const Color overlay = Color(0x80000000);
  static const Color overlayLight = Color(0x40000000);
  static const Color overlayDark = Color(0xB3000000);

  // Shimmer colors for loading states
  static const Color shimmerBase = Color(0xFF2A2B2C);
  static const Color shimmerHighlight = Color(0xFF3A3B3C);
}
