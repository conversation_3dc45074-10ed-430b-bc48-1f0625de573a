import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../theme/app_colors.dart';
import '../utils/api_path.dart';
import '../services/image_error_handler.dart';

/// A robust network image widget that handles various error scenarios
/// including Android ImageDecoder errors, network issues, and corrupted images
class RobustNetworkImage extends StatefulWidget {
  final String? imageUrl;
  final double width;
  final double height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final Color? backgroundColor;
  final int maxRetries;
  final Duration retryDelay;
  final bool enableCaching;
  final Map<String, String>? headers;

  const RobustNetworkImage({
    super.key,
    required this.imageUrl,
    required this.width,
    required this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.placeholder,
    this.errorWidget,
    this.backgroundColor,
    this.maxRetries = 2,
    this.retryDelay = const Duration(seconds: 1),
    this.enableCaching = true,
    this.headers,
  });

  @override
  State<RobustNetworkImage> createState() => _RobustNetworkImageState();
}

class _RobustNetworkImageState extends State<RobustNetworkImage> {
  int _retryCount = 0;
  bool _hasError = false;
  String? _processedImageUrl;
  Object? _lastError;

  @override
  void initState() {
    super.initState();
    _processImageUrl();
  }

  @override
  void didUpdateWidget(RobustNetworkImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _retryCount = 0;
      _hasError = false;
      _processImageUrl();
    }
  }

  void _processImageUrl() {
    if (widget.imageUrl == null || widget.imageUrl!.isEmpty) {
      setState(() {
        _processedImageUrl = null;
        _hasError = true;
      });
      return;
    }

    String url = widget.imageUrl!.trim();

    // Handle relative URLs by prepending base URL
    if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('data:')) {
      // Only prepend base URL if it's not already a complete URL
      url = '${ApiPath.baseUrlFile}$url';
    }

    // Fix malformed URLs
    if (url.startsWith('//')) {
      url = 'https:$url';
    }

    // Add cache-busting parameter if caching is disabled
    if (!widget.enableCaching) {
      final separator = url.contains('?') ? '&' : '?';
      url = '$url${separator}t=${DateTime.now().millisecondsSinceEpoch}';
    }

    setState(() {
      _processedImageUrl = url;
      _hasError = false;
      _lastError = null;
    });

    // Debug print to help troubleshoot
    debugPrint('RobustNetworkImage: Processing URL: $url');
  }

  Future<void> _retryLoad() async {
    if (_retryCount < widget.maxRetries && _lastError != null && ImageErrorHandler.shouldRetry(_lastError!)) {
      setState(() {
        _retryCount++;
        _hasError = false;
      });

      // Use smart retry delay based on error type
      final retryDelay = ImageErrorHandler.getRetryDelay(_lastError!, _retryCount);
      await Future.delayed(retryDelay);

      if (mounted) {
        _processImageUrl();
      }
    }
  }

  void _handleError(Object error, StackTrace? stackTrace) {
    if (mounted) {
      setState(() {
        _hasError = true;
        _lastError = error;
      });

      // Log the error using the error handler
      ImageErrorHandler.logImageError(_processedImageUrl, error, stackTrace);

      // Attempt retry if we haven't exceeded max retries and error is retryable
      if (_retryCount < widget.maxRetries && ImageErrorHandler.shouldRetry(error)) {
        _retryLoad();
      }
    }
  }

  Widget _buildPlaceholder() {
    return widget.placeholder ?? Container(
      width: widget.width,
      height: widget.height,
      color: widget.backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
      child: Center(
        child: SizedBox(
          width: 20.w,
          height: 20.w,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              AppColors.cFirstColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return widget.errorWidget ?? Builder(
        builder: (context) => Container(
          width: widget.width,
          height: widget.height,
          color: widget.backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.image_not_supported_outlined,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                size: (widget.width * 0.4).clamp(16.0, 32.0),
              ),
              if (_lastError != null) ...[
                SizedBox(height: 4.h),
                Text(
                  ImageErrorHandler.getUserFriendlyMessage(_lastError!),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppColors.cTextGrayColor,
                    fontSize: 8.sp,
                    height: 1.1,
                  ),
                ),
              ],
              if (_retryCount < widget.maxRetries &&
                  _lastError != null &&
                  ImageErrorHandler.shouldRetry(_lastError!)) ...[
                SizedBox(height: 4.h),
                GestureDetector(
                  onTap: _retryLoad,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      'Qayta',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ));
    }

  Widget _buildImageContent() {
    if (_processedImageUrl == null) {
      return _buildErrorWidget();
    }

    if (_hasError) {
      return _buildErrorWidget();
    }

    debugPrint('RobustNetworkImage: Loading image: $_processedImageUrl');

    return Image.network(
      _processedImageUrl!,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      headers: widget.headers,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          debugPrint('RobustNetworkImage: Image loaded successfully');
          return child;
        }
        debugPrint('RobustNetworkImage: Loading progress: ${loadingProgress.cumulativeBytesLoaded}/${loadingProgress.expectedTotalBytes}');
        return _buildPlaceholder();
      },
      errorBuilder: (context, error, stackTrace) {
        debugPrint('RobustNetworkImage: Error loading image: $error');
        _handleError(error, stackTrace);
        return _buildErrorWidget();
      },
      // Add frame builder to handle additional error cases
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded || frame != null) {
          return child;
        }
        return _buildPlaceholder();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = _buildImageContent();

    if (widget.borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }
}
