import 'dart:io';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/core/widgets/universal_loading.dart';
import 'package:click_bazaar/env.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../theme/app_colors.dart';
import '../../generated/assets.dart';

/// Universal avatar widget for profile images
/// Supports network images, local files, and fallback states
class UniversalAvatar extends StatelessWidget {
  final String? imageUrl;
  final File? imageFile;
  final double size;
  final bool showCameraIcon;
  final VoidCallback? onCameraTap;
  final bool isLoading;
  final String? fallbackText;
  final Color? backgroundColor;
  final Color? borderColor;
  final double borderWidth;
  final Color? placeholderBorderColor;
  final double placeholderBorderWidth;

  const UniversalAvatar({
    super.key,
    this.imageUrl,
    this.imageFile,
    this.size = 120,
    this.showCameraIcon = false,
    this.onCameraTap,
    this.isLoading = false,
    this.fallbackText,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth = 0,
    this.placeholderBorderColor,
    this.placeholderBorderWidth = 1,
  });

  /// Factory constructor for profile avatars with camera functionality
  factory UniversalAvatar.profile({
    Key? key,
    String? imageUrl,
    File? imageFile,
    double size = 120,
    VoidCallback? onCameraTap,
    bool isLoading = false,
    String? fallbackText,
    Color? placeholderBorderColor,
    double placeholderBorderWidth = 1,
  }) {
    return UniversalAvatar(
      key: key,
      imageUrl: imageUrl,
      imageFile: imageFile,
      size: size,
      showCameraIcon: onCameraTap != null,
      onCameraTap: onCameraTap,
      isLoading: isLoading,
      fallbackText: fallbackText,
      placeholderBorderColor: placeholderBorderColor,
      placeholderBorderWidth: placeholderBorderWidth,
    );
  }

  /// Factory constructor for small avatars (like in lists)
  factory UniversalAvatar.small({
    Key? key,
    String? imageUrl,
    File? imageFile,
    double size = 40,
    bool isLoading = false,
    String? fallbackText,
    Color? backgroundColor,
    Color? placeholderBorderColor,
    double placeholderBorderWidth = 1,
  }) {
    return UniversalAvatar(
      key: key,
      imageUrl: imageUrl,
      imageFile: imageFile,
      size: size,
      showCameraIcon: false,
      isLoading: isLoading,
      fallbackText: fallbackText,
      backgroundColor: backgroundColor,
      placeholderBorderColor: placeholderBorderColor,
      placeholderBorderWidth: placeholderBorderWidth,
    );
  }

  /// Factory constructor for medium avatars
  factory UniversalAvatar.medium({
    Key? key,
    String? imageUrl,
    File? imageFile,
    double size = 80,
    bool isLoading = false,
    String? fallbackText,
    Color? backgroundColor,
    Color? placeholderBorderColor,
    double placeholderBorderWidth = 1,
  }) {
    return UniversalAvatar(
      key: key,
      imageUrl: imageUrl,
      imageFile: imageFile,
      size: size,
      showCameraIcon: false,
      isLoading: isLoading,
      fallbackText: fallbackText,
      backgroundColor: backgroundColor,
      placeholderBorderColor: placeholderBorderColor,
      placeholderBorderWidth: placeholderBorderWidth,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: showCameraIcon ? onCameraTap : null,
      child: Stack(
        children: [
          Container(
            width: size.w,
            height: size.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: borderWidth > 0
                  ? Border.all(
                      color: borderColor ?? AppColors.cFirstColor,
                      width: borderWidth,
                    )
                  : null,
            ),
            child: ClipOval(
              child: _buildAvatarContent(context),
            ),
          ),
          if (showCameraIcon && onCameraTap != null)
            Positioned(
              bottom: 0,
              right: 0,
              child: _buildCameraIcon(),
            ),
          // if (isLoading)
          //   Positioned.fill(
          //     child: UniversalLoading.shimmer(
          //       child: Container(
          //         decoration: BoxDecoration(
          //           shape: BoxShape.circle,
          //           color: AppColors.white,
          //         ),
          //         child: Center(
          //           child: SizedBox(
          //             width: (size * 0.3).w,
          //             height: (size * 0.3).w,
          //             child: CircularProgressIndicator(
          //               strokeWidth: 2.w,
          //               valueColor: AlwaysStoppedAnimation<Color>(
          //                 AppColors.cFirstColor,
          //               ),
          //             ),
          //           ),
          //         ),
          //       ),
          //     ),
          //   ),
        ],
      ),
    );
  }

  Widget _buildAvatarContent(BuildContext context) {
    // Priority: Local file > Network image > Fallback
    if (imageFile != null) {
      return Image.file(
        imageFile!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildFallback(context),
      );
    }

    if (imageUrl != null && imageUrl!.isNotEmpty) {
      return Image.network(
        ApiPath.baseUrlFile + imageUrl!,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return _buildLoadingPlaceholder();
        },
        errorBuilder: (context, error, stackTrace) => _buildFallback(context),
      );
    }

    return _buildFallback(context);
  }

  Widget _buildFallback(BuildContext context) {
    var isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ??
            (isDark ? AppColors.cCardsColor : AppColors.cFirstColor),
        shape: BoxShape.circle,
        border: Border.all(
          color: placeholderBorderColor ??
              AppColors.cTextGrayColor.withValues(alpha: 0.3),
          width: placeholderBorderWidth.w,
        ),
      ),
      child: Center(
        child: fallbackText != null && fallbackText?.isNotEmpty == true
            ? Text(
                _getInitials(fallbackText ?? ""),
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: (size * 0.4).sp,
                  fontWeight: FontWeight.w600,
                ),
              )
            : Icon(
                Icons.person,
                color: AppColors.white,
                size: (size * 0.5).w,
              ),
      ),
    );
  }

  Widget _buildLoadingPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.cCardsColor,
        shape: BoxShape.circle,
        border: Border.all(
          color: placeholderBorderColor ??
              AppColors.cTextGrayColor.withValues(alpha: 0.3),
          width: placeholderBorderWidth.w,
        ),
      ),
      child: Center(
        child: SizedBox(
          width: (size * 0.3).w,
          height: (size * 0.3).w,
          child: CircularProgressIndicator(
            strokeWidth: 2.w,
            valueColor: AlwaysStoppedAnimation<Color>(
              AppColors.cFirstColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCameraIcon() {
    return Container(
      width: (size * 0.3).w,
      height: (size * 0.3).w,
      decoration: BoxDecoration(
        color: AppColors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: AppColors.cBackgroundColor.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all((size * 0.05).w),
        child: SvgPicture.asset(
          Assets.iconsCamera,
          colorFilter: ColorFilter.mode(
            AppColors.cBackgroundColor,
            BlendMode.srcIn,
          ),
        ),
      ),
    );
  }

  String _getInitials(String name) {
    final words = name.trim().split(' ');
    if (words.isEmpty) return '';

    if (words.length == 1) {
      final word = words[0];
      return word.isEmpty ? '' : word.substring(0, 1).toUpperCase();
    }

    final firstWord = words[0];
    final secondWord = words[1];

    return '${firstWord.isEmpty ? '' : firstWord.substring(0, 1)}${secondWord.isEmpty ? '' : secondWord.substring(0, 1)}'
        .toUpperCase();
  }
}
