import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/core/widgets/role_switcher.dart';
import 'package:dio/dio.dart';

class LoginRemoteDatasourceImpl {
  final NetworkInfo networkInfo;
  final Dio dio;

  LoginRemoteDatasourceImpl({
    required this.networkInfo,
    required this.dio,
  });

  Future<bool> login(
      String phone, String appSignature, UserRole userRole) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: userRole == UserRole.nazoratchi
              ? ApiPath.nazLoginPath
              : ApiPath.sotLoginPath,
        ),
        type: DioExceptionType.connectionError,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
      );
    }
    try {
      var response = await dio.post(
        userRole == UserRole.nazoratchi
            ? ApiPath.nazLoginPath
            : ApiPath.sotLoginPath,
        data: {
          'phone': phone,
          'appSignature': appSignature,
        },
      );
      final data = response.data;
      if (response.statusCode == 200) {
        if (data.containsKey('message')) {
          return data['message'] == 'success';
        }
        return false;
      }
      return false;
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.nazLoginPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }
}
