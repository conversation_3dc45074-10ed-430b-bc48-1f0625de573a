import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import 'package:click_bazaar/core/widgets/role_switcher.dart';
import 'package:click_bazaar/features/auth/datasources/login_remote_datasources.dart';
import 'package:equatable/equatable.dart';

part 'login_event.dart';

part 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState>
    with ErrorHandlerMixin<LoginEvent, LoginState> {
  final NetworkInfo networkInfo;
  final LoginRemoteDatasourceImpl loginRemoteDatasource;

  LoginBloc({required this.networkInfo, required this.loginRemoteDatasource})
      : super(LoginState()) {
    on<LoginEvent>(_handleLoginWithMixin);
  }
  Future<void> _handleLoginWithMixin(
      LoginEvent event, Emitter<LoginState> emit) async {

    await executeApiCall<bool>(
      apiCall: () => loginRemoteDatasource.login(
          event.phone, event.appSignature, event.userRole),
      onLoading: () => emit(LoginState(status: LoginStatus.loading)),
      onSuccess: (response) {
        if (response) {
          emit(LoginState(status: LoginStatus.success));
        } else {
          emit(LoginState(
              status: LoginStatus.failure,
              message:
                  'Kirish muvaffaqiyatsiz. Iltimos, qayta urinib ko\'ring.'));
        }
      },
      onFailure: (message) =>
          emit(LoginState(status: LoginStatus.failure, message: message)),
    );
  }

  // Future<void> _handleLogin(LoginEvent event, Emitter<LoginState> emit) async {
  //   emit(LoginState(status: LoginStatus.loading));
  //
  //   try {
  //     var response = await loginRemoteDatasource.login(
  //         event.phone, event.appSignature, event.userRole);
  //     print("Result:${response}");
  //     if (response) {
  //       emit(LoginState(status: LoginStatus.success));
  //     } else {
  //       emit(LoginState(
  //           status: LoginStatus.failure,
  //           message:
  //           'Kirish muvaffaqiyatsiz. Iltimos, qayta urinib ko\'ring.'));
  //     }
  //   } on DioException catch (e) {
  //     // Use universal Dio error handling
  //     final errorMessage = SimpleErrorHandler.handleError(e);
  //     emit(LoginState(status: LoginStatus.failure, message: errorMessage));
  //
  //     // Handle specific cases
  //     if (SimpleErrorHandler.requiresReauth(e)) {
  //       // Handle logout if needed
  //       // You can add logout logic here
  //     }
  //   } catch (e) {
  //     // Handle any other exceptions
  //     final errorMessage = SimpleErrorHandler.handleError(e);
  //     emit(LoginState(status: LoginStatus.failure, message: errorMessage));
  //   }
  // }
}
