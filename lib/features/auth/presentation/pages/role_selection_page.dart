import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/app/main_app_wrapper.dart';
import '../../../../core/widgets/role_switcher.dart';
import '../../models/auth_models.dart';

///Only in demo mode
class RoleSelectionPage extends StatefulWidget {
  const RoleSelectionPage({super.key});

  @override
  State<RoleSelectionPage> createState() => _RoleSelectionPageState();
}

class _RoleSelectionPageState extends State<RoleSelectionPage> {
  UserRole? _selectedRole;

  void _selectRole(UserRole role) {
    setState(() {
      _selectedRole = role;
    });
  }

  void _continueWithRole() {
    if (_selectedRole == null) return;

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => MainAppWrapper(
          initialRole: _selectedRole!,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Gap(60.h),
              
              // Title
              Text(
                'Rolni tanlang',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),

              Gap(8.h),

              // Subtitle
              Text(
                'Qaysi rol sifatida\nilovadan foydalanmoqchisiz?',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  height: 1.4,
                ),
              ),
              
              Gap(40.h),
              
              // Role options
              _buildRoleOption(
                role: UserRole.sotuvchi,
                title: 'Sotuvchi',
                description: 'Savdo-sotiq bilan shug\'ullanuvchi',
                isSelected: _selectedRole == UserRole.sotuvchi,
                onTap: () => _selectRole(UserRole.sotuvchi),
              ),
              
              Gap(16.h),
              
              _buildRoleOption(
                role: UserRole.nazoratchi,
                title: 'Nazoratchi',
                description: 'Nazorat qiluvchi xodim',
                isSelected: _selectedRole == UserRole.nazoratchi,
                onTap: () => _selectRole(UserRole.nazoratchi),
              ),
              
              const Spacer(),
              
              // Continue button
              SizedBox(
                width: double.infinity,
                height: 52.h,
                child: ElevatedButton(
                  onPressed: _selectedRole != null ? _continueWithRole : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    elevation: 0,
                    disabledBackgroundColor: Theme.of(context).colorScheme.surface,
                    disabledForegroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  child: Text(
                    'Davom etish',
                    style: AppTextStyles.buttonText,
                  ),
                ),
              ),
              
              Gap(40.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleOption({
    required UserRole role,
    required String title,
    required String description,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected ? Theme.of(context).colorScheme.primary : Colors.transparent,
            width: 2,
          ),
        ),
        child: Row(
          children: [
            // Radio button
            Container(
              width: 20.w,
              height: 20.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.onSurfaceVariant,
                  width: 2,
                ),
                color: isSelected ? Theme.of(context).colorScheme.primary : Colors.transparent,
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 8.w,
                        height: 8.h,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
                      ),
                    )
                  : null,
            ),
            
            Gap(16.w),
            
            // Role info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Gap(4.h),
                  Text(
                    description,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.cTextGrayColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
