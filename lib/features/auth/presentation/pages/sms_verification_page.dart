import 'dart:async';
import 'package:auto_sms_verification/auto_sms_verification.dart';
import 'package:click_bazaar/core/widgets/role_switcher.dart';
import 'package:click_bazaar/features/face_control/lock_switcher.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../di/dependency_injection.dart';
import '../../../../main_naz_navigation_page.dart';
import '../../../../main_sot_navigation_page.dart';
import '../../services/auth_service.dart';
import '../bloc/sms_verification_bloc/sms_verification_bloc.dart';
import '../widgets/sms_code_input.dart';
import 'role_selection_page.dart';

class SmsVerificationPage extends StatelessWidget {
  final String phoneNumber;
  final UserRole userRole;

  const SmsVerificationPage({
    super.key,
    required this.phoneNumber,
    required this.userRole,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di<SmsVerificationBloc>(),
      child: _SmsVerificationPageContent(
        phoneNumber: phoneNumber,
        userRole: userRole,
      ),
    );
  }
}

class _SmsVerificationPageContent extends StatefulWidget {
  final String phoneNumber;
  final UserRole userRole;

  const _SmsVerificationPageContent({
    required this.phoneNumber,
    required this.userRole,
  });

  @override
  State<_SmsVerificationPageContent> createState() =>
      _SmsVerificationPageContentState();
}

class _SmsVerificationPageContentState
    extends State<_SmsVerificationPageContent> {
  String _smsCode = '';
  bool _isLoading = false;
  int _countdown = 59;
  Timer? _timer;

  late final AuthService _authService;
  final GlobalKey<SmsCodeInputState> _smsCodeInputKey =
      GlobalKey<SmsCodeInputState>();

  @override
  void initState() {
    super.initState();
    _authService = AuthService(
      dio: di(),
      storage: di(),
      networkInfo: di(),
    );
    _startCountdown();
    _startAutoSmsVerification();
  }

  @override
  void dispose() {
    _timer?.cancel();
    AutoSmsVerification.stopListening();
    super.dispose();
  }

  void _startAutoSmsVerification() async {
    try {
      final sms = await AutoSmsVerification.startListeningSms();
      if (sms != null && mounted) {
        // Extract 6-digit code from SMS
        final code = _extractOtpFromSms(sms);
        if (code != null) {
          // Auto-fill the SMS code using the key
          if (mounted && _smsCodeInputKey.currentState != null) {
            _smsCodeInputKey.currentState!.setSmsCode(code);
            setState(() {
              _smsCode = code;
            });
            // Auto-verify after a short delay
            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted && _smsCode.length == 6) {
                _verifySmsCode();
              }
            });
          }
        }
      }
    } catch (e) {
      print('Error setting up auto SMS verification: $e');
    }
  }

  String? _extractOtpFromSms(String sms) {
    // Extract 6-digit code from SMS
    final RegExp regExp = RegExp(r'\b\d{6}\b');
    final match = regExp.firstMatch(sms);
    return match?.group(0);
  }

  void _startCountdown() {
    // Cancel any existing timer before starting a new one
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        setState(() {
          _countdown--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  String _formatCountdown(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _verifySmsCode() async {
    if (_smsCode.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('6 xonali SMS kodni kiriting'),
          backgroundColor: AppColors.cReddishColor,
        ),
      );
      return;
    }

    // Extract phone number without formatting
    String phoneNumber =
        widget.phoneNumber.trim().replaceAll('+998 ', '').replaceAll(' ', '');

    // Guard the BuildContext usage with mounted check
    if (!mounted) return;

    // Trigger SMS verification via BLoC
    context.read<SmsVerificationBloc>().add(VerifySmsCodeEvent(
          phone: phoneNumber,
          verifyCode: _smsCode,
          userRole: widget.userRole,
        ));
  }

  Future<void> _resendCode() async {
    // Only allow resend when countdown is 0
    if (_countdown > 0 || _isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Use the real SMS resend API with the same endpoints as login
      final response = await _authService.resendSmsCode(
        widget.phoneNumber,
        widget.userRole,
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        if (response.success) {
          _countdown = 59;
          _startCountdown();
        }
      });

      if (response.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('SMS kod qayta yuborildi'),
            backgroundColor: AppColors.cGreenishColor,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.message ?? 'Xatolik yuz berdi'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Xatolik yuz berdi'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SmsVerificationBloc, SmsVerificationState>(
      listener: (context, state) {
        if (state.status == SmsVerificationStatus.loading) {
          setState(() {
            _isLoading = true;
          });
        } else {
          setState(() {
            _isLoading = false;
          });
        }

        if (state.status == SmsVerificationStatus.success) {
          // Navigate based on user role
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => LockProvider(),
            ),
          );
        } else if (state.status == SmsVerificationStatus.failure) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message ?? 'SMS kod tasdiqlanmadi'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          appBar: AppBar(
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            elevation: 0,
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Gap(20.h),

                  // Title
                  Text(
                    'Kodni kiriting',
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),

                  Gap(8.h),

                  // Subtitle with phone number
                  RichText(
                    text: TextSpan(
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        height: 1.4,
                      ),
                      children: [
                        TextSpan(
                          text: widget.phoneNumber,
                          style: AppTextStyles.bodyLarge.copyWith(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const TextSpan(
                          text:
                              ' raqamingizga\nyuborilgan 6 xonali SMS kodni kiriting',
                        ),
                      ],
                    ),
                  ),

                  Gap(40.h),

                  // SMS code input
                  SmsCodeInput(
                    key: _smsCodeInputKey,
                    onCompleted: (code) {
                      setState(() {
                        _smsCode = code;
                      });
                    },
                    onChanged: (code) {
                      setState(() {
                        _smsCode = code;
                      });
                    },
                  ),

                  Gap(32.h),

                  // Countdown timer
                  Center(
                    child: Text(
                      _formatCountdown(_countdown),
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  Gap(40.h),

                  // Verify button
                  SizedBox(
                    width: double.infinity,
                    height: 52.h,
                    child: ElevatedButton(
                      onPressed: _isLoading || _smsCode.length != 6
                          ? null
                          : _verifySmsCode,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        elevation: 0,
                      ),
                      child: _isLoading
                          ? SizedBox(
                              width: 20.h,
                              height: 20.h,
                              child: CircularProgressIndicator(
                                color: Theme.of(context).colorScheme.onPrimary,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              'Yuborish',
                              style: AppTextStyles.buttonText,
                            ),
                    ),
                  ),

                  Gap(24.h),

                  // Resend code link
                  Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Kodni olmadingizmi? ',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        Flexible(
                          child: TextButton(
                            onPressed: _countdown == 0 && !_isLoading
                                ? _resendCode
                                : null,
                            child: Text(
                              _isLoading
                                  ? 'Yuborilmoqda...'
                                  : _countdown > 0
                                      ? 'Qayta yuborish (${_formatCountdown(_countdown)})'
                                      : 'Qayta yuborish',
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: _countdown == 0 && !_isLoading
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(context).colorScheme.onSurfaceVariant,
                                fontWeight: FontWeight.w600,
                                decoration: _countdown == 0 && !_isLoading
                                    ? TextDecoration.underline
                                    : null,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const Spacer(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
