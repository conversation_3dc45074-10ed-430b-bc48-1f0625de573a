import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:auto_sms_verification/auto_sms_verification.dart';
import '../../../core/network/network_info.dart';
import '../../../core/utils/api_path.dart';
import '../../../core/utils/app_constants.dart';
import '../../../core/utils/app_functions.dart';
import '../../../core/widgets/role_switcher.dart';
import '../models/auth_models.dart';

/// Authentication service for handling login and SMS verification
class AuthService {
  final Dio _dio;
  final GetStorage _storage;
  final NetworkInfo _networkInfo;

  AuthService({
    required Dio dio,
    required GetStorage storage,
    required NetworkInfo networkInfo,
  })  : _dio = dio,
        _storage = storage,
        _networkInfo = networkInfo;

  /// Send SMS code to phone number
  Future<AuthResponse> sendSmsCode(LoginRequest request) async {
    try {
      // For demo purposes, always return success
      // In real implementation, this would make an API call
      await Future.delayed(const Duration(milliseconds: 500));

      return const AuthResponse(
        success: true,
        message: 'SMS kod yuborildi',
      );

      /* Real API implementation:
      final response = await _dio.post(
        '${ApiPath.baseUrl}/auth/send-sms',
        data: request.toJson(),
      );

      return AuthResponse.fromJson(response.data);
      */
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'Xatolik yuz berdi: $e',
      );
    }
  }

  /// Send SMS code for resend functionality using real API
  Future<AuthResponse> resendSmsCode(
      String phoneNumber, UserRole userRole) async {
    try {
      // Check network connectivity first
      if (!await _networkInfo.isConnected) {
        return const AuthResponse(
          success: false,
          message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
        );
      }

      // Extract phone number without formatting for API call
      String cleanPhoneNumber =
          phoneNumber.trim().replaceAll('+998 ', '').replaceAll(' ', '');

      // Get app signature for Android
      String appSignature = 'undefined';
      try {
        appSignature = await AutoSmsVerification.appSignature() ?? 'undefined';
      } catch (e) {
        // Ignore error and use default signature
        appSignature = 'undefined';
      }

      // Use the same API endpoints as login flow
      final response = await _dio.post(
        userRole == UserRole.nazoratchi
            ? ApiPath.nazLoginPath
            : ApiPath.sotLoginPath,
        data: {
          'phone': cleanPhoneNumber,
          'appSignature': appSignature,
        },
      );

      final data = response.data;
      if (response.statusCode == 200) {
        if (data.containsKey('message') && data['message'] == 'success') {
          return const AuthResponse(
            success: true,
            message: 'SMS kod qayta yuborildi',
          );
        } else {
          return AuthResponse(
            success: false,
            message: data['message'] ?? 'SMS kod yuborishda xatolik',
          );
        }
      } else {
        return const AuthResponse(
          success: false,
          message: 'Server xatoligi',
        );
      }
    } on DioException catch (e) {
      // Handle Dio exceptions
      String errorMessage = 'Xatolik yuz berdi';
      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = 'Ulanish vaqti tugadi';
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Javob kutish vaqti tugadi';
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'Internet aloqasi yo\'q';
      } else if (e.response?.statusCode == 400) {
        errorMessage = 'Noto\'g\'ri ma\'lumot';
      } else if (e.response?.statusCode == 500) {
        errorMessage = 'Server xatoligi';
      }

      return AuthResponse(
        success: false,
        message: errorMessage,
      );
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'Kutilmagan xatolik yuz berdi',
      );
    }
  }

  /// Verify SMS code and authenticate user
  Future<AuthResponse> verifySmsCode(SmsVerificationRequest request) async {
    try {
      // For demo purposes, accept any 6-digit code
      if (request.smsCode.length == 6) {
        await Future.delayed(const Duration(milliseconds: 500));

        // Store demo token
        await _storage.write(
            TOKEN, 'demo_token_${DateTime.now().millisecondsSinceEpoch}');
        await _storage.write(is_demo, true);

        return const AuthResponse(
          success: true,
          token: 'demo_token',
          message: 'Muvaffaqiyatli kirish',
        );
      } else {
        return const AuthResponse(
          success: false,
          message: 'Noto\'g\'ri SMS kod',
        );
      }

      /* Real API implementation:
      final response = await _dio.post(
        '${ApiPath.baseUrl}/auth/verify-sms',
        data: request.toJson(),
      );

      final authResponse = AuthResponse.fromJson(response.data);
      
      if (authResponse.success && authResponse.token != null) {
        await _storage.write(TOKEN, authResponse.token);
        if (authResponse.refreshToken != null) {
          await _storage.write(REFRESH_TOKEN, authResponse.refreshToken);
        }
      }

      return authResponse;
      */
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'Xatolik yuz berdi: $e',
      );
    }
  }

  /// Demo login without SMS verification (true guest mode)
  Future<AuthResponse> demoLogin() async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));

      // Clear any existing tokens for true guest mode
      await _storage.remove(TOKEN);
      await _storage.remove(REFRESH_TOKEN);
      // Set demo flag to indicate guest mode
      await _storage.write(is_demo, true);

      return const AuthResponse(
        success: true,
        token: null, // No token for true guest mode
        message: 'Demo rejimda kirish',
      );
    } catch (e) {
      return AuthResponse(
        success: false,
        message: 'Demo kirish xatoligi: $e',
      );
    }
  }

  /// Check if user is authenticated
  bool get isAuthenticated {
    return _storage.read(TOKEN) != null;
  }

  /// Check if in demo mode
  bool get isDemoMode {
    return _storage.read(is_demo) ?? false;
  }

  // /// Logout user (basic logout - for compatibility)
  // Future<void> logout() async {
  //   await _storage.remove(TOKEN);
  //   await _storage.remove(REFRESH_TOKEN);
  //   await _storage.remove(is_demo);
  //   await _storage.remove(USER_ROLE);
  //   await _storage.remove(USER_ID);
  //   await _storage.remove(USER_PROFILE);
  //   await _storage.remove(MARKET_ID);
  // }
  //
  // /// Complete logout with full storage erasure
  // Future<void> completeLogout() async {
  //   try {
  //     await _storage.erase();
  //   } catch (e) {
  //     // Fallback to individual key removal
  //     await logout();
  //   }
  // }
}
