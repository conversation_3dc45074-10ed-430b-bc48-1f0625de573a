import 'package:flutter/material.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.primary,
        title: Text(
          'Premium Soft',
          style: TextStyle(color: Theme.of(context).colorScheme.onPrimary),
        ),
        toolbarHeight: 70,
        centerTitle: true,
      ),
      body: Container(
          margin: const EdgeInsets.only(left: 16.0, right: 16.0),
          child: const Column(children: [
            SizedBox(height: 10),
            Text(
                "Hi! I'm developer. If you have trouble with Face control, contact me!", textAlign: TextAlign.center,),
            <PERSON>zed<PERSON>ox(height: 4),
            <PERSON>(
              children: [
                Icon(
                  Icons.email,
                  color: Colors.black,
                  weight: 24,
                ),
                SizedBox(width: 4),
                Text('Email: <EMAIL>')
              ],
            ),
            Sized<PERSON><PERSON>(height: 4),
            Row(
              children: [
                Icon(
                  Icons.phone,
                  color: Colors.black,
                  weight: 24,
                ),
                SizedBox(width: 4),
                Text('Tel: +998911283725')
              ],
            ),
            SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.telegram,
                  color: Colors.black,
                  weight: 24,
                ),
                SizedBox(width: 4),
                Text('Telegram: @flutterblogs')
              ],
            ),
            SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.code,
                  color: Colors.black,
                  weight: 24,
                ),
                SizedBox(width: 4),
                Text('Github: https://github.com/mamasodikov')
              ],
            ),
          ])),
    );
  }
}
