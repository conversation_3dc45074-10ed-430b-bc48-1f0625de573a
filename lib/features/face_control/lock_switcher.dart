import 'dart:io';

import 'package:click_bazaar/core/app/main_app_wrapper.dart';
import 'package:click_bazaar/core/widgets/role_switcher.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/di/dependency_injection.dart' as di;
import 'package:click_bazaar/features/face_control/bio_lock_page.dart';
import 'package:click_bazaar/features/face_control/functional_lock_page.dart';
import 'package:click_bazaar/main_naz_navigation_page.dart';

// ignore: must_be_immutable
class LockProvider extends StatelessWidget {
  final GetStorage storage = di.di();

  var emulator = EMULATOR;

  @override
  Widget build(BuildContext context) {
    var isDemo = storage.read(is_demo) ?? false;
    bool isServerApproved = storage.read(server_approved) ?? false;
    bool isLocalApproved = storage.read(local_approved) ?? false;

    bool isTimeCorrect = storage.read(is_time_correct) ?? false;
    bool isGpsOn = storage.read(is_gps_active) ?? false;
    bool isNotMocked = storage.read(is_not_mocked) ?? false;
    String userRole = storage.read(USER_ROLE);

    print('Server approved: $isServerApproved\n');
    print('Local approved: $isLocalApproved\n');
    print('Time approved: $isTimeCorrect\n');
    print('GPS approved: $isGpsOn\n');
    print('MOCK approved: $isNotMocked\n');

    if (isDemo) {
      emulator = true;
    }

    return (!emulator && userRole == 'supervisor'
            ? isServerApproved && isLocalApproved
            : true)
        ? isTimeCorrect && isGpsOn && isNotMocked
            ? MainAppWrapper(
                initialRole: userRole == 'supervisor'
                    ? UserRole.nazoratchi
                    : UserRole.sotuvchi)
            : const FunctionalLockPage()
        : BioLockPage();
  }
}
