import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import 'package:click_bazaar/core/function/functions.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_storage/get_storage.dart';
import '../../../../auth/models/user_profile_model.dart';
import '../../datasources/naz_profile_remote_datasource.dart';
import 'naz_profile_event.dart';
import 'naz_profile_state.dart';

/// BLoC for managing naz profile state
class NazProfileBloc extends Bloc<NazProfileEvent, NazProfileState>
    with ErrorHandlerMixin<NazProfileEvent, NazProfileState> {
  final NazProfileRemoteDatasource _remoteDatasource;
  final GetStorage _storage;
  final NetworkInfo _networkInfo;

  NazProfileBloc({
    required NazProfileRemoteDatasource remoteDatasource,
    required GetStorage storage,
    required NetworkInfo networkInfo,
  })  : _remoteDatasource = remoteDatasource,
        _storage = storage,
        _networkInfo = networkInfo,
        super(const NazProfileInitial()) {
    on<LoadNazProfileEvent>(_onLoadNazProfile);
    on<RefreshNazProfileEvent>(_onRefreshNazProfile);
    on<ClearNazProfileEvent>(_onClearNazProfile);
    on<LoadCachedNazProfileEvent>(_onLoadCachedNazProfile);
    on<UpdateNazProfileImageEvent>(_onUpdateNazProfileImage);
  }

  /// Handle load naz profile event
  Future<void> _onLoadNazProfile(
    LoadNazProfileEvent event,
    Emitter<NazProfileState> emit,
  ) async {
    // Check if we should use cached data first
    if (!event.forceRefresh) {
      final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
        cacheKey: CacheKeys.nazoratchiProfile,
        fromJsonFunction: (json) => UserProfile.fromJson(json),
      );
      if (cachedProfile != null) {
        emit(NazProfileLoaded(
          userProfile: cachedProfile,
          isFromCache: true,
          message: 'Profile loaded from cache',
        ));
        return;
      }
    }

    emit(const NazProfileLoading());

    try {
      // Check network connectivity
      if (!await isNetworkConnected()) {
        final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
          cacheKey: CacheKeys.nazoratchiProfile,
          fromJsonFunction: (json) => UserProfile.fromJson(json),
        );
        emit(NazProfileError(
          message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
          cachedProfile: cachedProfile,
        ));
        return;
      }

      final response = await _remoteDatasource.getUserProfile(event.userId);

      if (response.success && response.userProfile != null) {
        // Cache the profile
        await CacheHelper.cacheUserProfile<UserProfile>(
          cacheKey: CacheKeys.nazoratchiProfile,
          profile: response.userProfile!,
          toJsonFunction: () => response.userProfile!.toJson(),
        );

        emit(NazProfileLoaded(
          userProfile: response.userProfile!,
          isFromCache: false,
          message: response.message,
        ));
      } else {
        // Try to get cached profile as fallback
        final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
          cacheKey: CacheKeys.nazoratchiProfile,
          fromJsonFunction: (json) => UserProfile.fromJson(json),
        );
        emit(NazProfileError(
          message: response.error ?? 'Failed to load profile',
          cachedProfile: cachedProfile,
        ));
      }
    } catch (e) {
      // Handle errors using DioErrorHandler
      final errorMessage =SimpleErrorHandler.handleError(e);
      final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
        cacheKey: CacheKeys.nazoratchiProfile,
        fromJsonFunction: (json) => UserProfile.fromJson(json),
      );
      emit(NazProfileError(
        message: errorMessage,
        cachedProfile: cachedProfile,
      ));
    }
  }

  /// Handle refresh naz profile event
  Future<void> _onRefreshNazProfile(
    RefreshNazProfileEvent event,
    Emitter<NazProfileState> emit,
  ) async {
    // If we have current profile, show refreshing state
    if (state is NazProfileLoaded) {
      final currentState = state as NazProfileLoaded;
      emit(NazProfileRefreshing(currentProfile: currentState.userProfile));
    } else {
      emit(const NazProfileLoading());
    }

    try {
      // Check network connectivity
      if (!await isNetworkConnected()) {
        final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
          cacheKey: CacheKeys.nazoratchiProfile,
          fromJsonFunction: (json) => UserProfile.fromJson(json),
        );
        emit(NazProfileError(
          message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
          cachedProfile: cachedProfile,
        ));
        return;
      }

      final response = await _remoteDatasource.getUserProfile(event.userId);

      if (response.success && response.userProfile != null) {
        // Cache the profile
        await CacheHelper.cacheUserProfile<UserProfile>(
          cacheKey: CacheKeys.nazoratchiProfile,
          profile: response.userProfile!,
          toJsonFunction: () => response.userProfile!.toJson(),
        );

        emit(NazProfileLoaded(
          userProfile: response.userProfile!,
          isFromCache: false,
          message: 'Profile refreshed successfully',
        ));
      } else {
        // Try to get cached profile as fallback
        final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
          cacheKey: CacheKeys.nazoratchiProfile,
          fromJsonFunction: (json) => UserProfile.fromJson(json),
        );
        emit(NazProfileError(
          message: response.error ?? 'Failed to refresh profile',
          cachedProfile: cachedProfile,
        ));
      }
    } catch (e) {
      // Handle errors using DioErrorHandler
      final errorMessage = SimpleErrorHandler.handleError(e);
      final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
        cacheKey: CacheKeys.nazoratchiProfile,
        fromJsonFunction: (json) => UserProfile.fromJson(json),
      );
      emit(NazProfileError(
        message: errorMessage,
        cachedProfile: cachedProfile,
      ));
    }
  }

  /// Handle clear naz profile event
  Future<void> _onClearNazProfile(
    ClearNazProfileEvent event,
    Emitter<NazProfileState> emit,
  ) async {
    try {
      await CacheHelper.clearCachedUserProfile(CacheKeys.nazoratchiProfile);
      emit(const NazProfileInitial());
    } catch (e) {
      // Even if clearing fails, reset to initial state
      emit(const NazProfileInitial());
    }
  }

  /// Handle load cached naz profile event
  Future<void> _onLoadCachedNazProfile(
    LoadCachedNazProfileEvent event,
    Emitter<NazProfileState> emit,
  ) async {
    try {
      final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
        cacheKey: CacheKeys.nazoratchiProfile,
        fromJsonFunction: (json) => UserProfile.fromJson(json),
      );

      if (cachedProfile != null) {
        emit(NazProfileLoaded(
          userProfile: cachedProfile,
          isFromCache: true,
          message: 'Profile loaded from cache',
        ));
      } else {
        emit(const NazProfileInitial());
      }
    } catch (e) {
      emit(const NazProfileError(
        message: 'Failed to load cached profile',
      ));
    }
  }

  /// Handle update naz profile image event
  Future<void> _onUpdateNazProfileImage(
    UpdateNazProfileImageEvent event,
    Emitter<NazProfileState> emit,
  ) async {
    // Get current profile for updating state
    UserProfile? currentProfile;
    if (state is NazProfileLoaded) {
      currentProfile = (state as NazProfileLoaded).userProfile;
    } else {
      currentProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
        cacheKey: CacheKeys.nazoratchiProfile,
        fromJsonFunction: (json) => UserProfile.fromJson(json),
      );
    }

    if (currentProfile != null) {
      emit(NazProfileImageUpdating(currentProfile: currentProfile));
    } else {
      emit(const NazProfileLoading());
    }

    try {
      // Check network connectivity
      if (!await isNetworkConnected()) {
        final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
          cacheKey: CacheKeys.nazoratchiProfile,
          fromJsonFunction: (json) => UserProfile.fromJson(json),
        );
        emit(NazProfileError(
          message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
          cachedProfile: cachedProfile,
        ));
        return;
      }

      final response = await _remoteDatasource.updateProfileImage(
        event.userId,
        event.imagePath,
      );

      if (response.success && response.userProfile != null) {
        // Cache the updated profile with new image URL
        await CacheHelper.cacheUserProfile<UserProfile>(
          cacheKey: CacheKeys.nazoratchiProfile,
          profile: response.userProfile!,
          toJsonFunction: () => response.userProfile!.toJson(),
        );

        emit(NazProfileImageUpdated(
          updatedProfile: response.userProfile!,
          message: 'Profil rasmi muvaffaqiyatli yangilandi',
        ));

        // Then emit loaded state with updated profile (contains new image URL)
        emit(NazProfileLoaded(
          userProfile: response.userProfile!,
          isFromCache: false,
          message: 'Profil rasmi yangilandi',
        ));
      } else {
        // On upload failure, reload the original profile from server to get the old image URL
        try {
          final originalProfileResponse = await _remoteDatasource.getUserProfile(event.userId);

          if (originalProfileResponse.success && originalProfileResponse.userProfile != null) {
            // Cache the reloaded original profile
            await CacheHelper.cacheUserProfile<UserProfile>(
              cacheKey: CacheKeys.nazoratchiProfile,
              profile: originalProfileResponse.userProfile!,
              toJsonFunction: () => originalProfileResponse.userProfile!.toJson(),
            );

            // First emit error for UI feedback
            emit(NazProfileError(
              message: response.error ?? 'Profil rasmini yangilashda xatolik yuz berdi',
              cachedProfile: originalProfileResponse.userProfile,
            ));

            // Then revert to loaded state with original profile (old image URL)
            emit(NazProfileLoaded(
              userProfile: originalProfileResponse.userProfile!,
              isFromCache: false,
              message: 'Eski profil rasmi qaytarildi',
            ));
          } else {
            // If can't reload from server, use cached profile as fallback
            final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
              cacheKey: CacheKeys.nazoratchiProfile,
              fromJsonFunction: (json) => UserProfile.fromJson(json),
            );
            emit(NazProfileError(
              message: response.error ?? 'Profil rasmini yangilashda xatolik yuz berdi',
              cachedProfile: cachedProfile,
            ));

            if (cachedProfile != null) {
              emit(NazProfileLoaded(
                userProfile: cachedProfile,
                isFromCache: true,
                message: 'Keshdan eski profil rasmi qaytarildi',
              ));
            }
          }
        } catch (reloadError) {
          // If reload fails, use cached profile
          final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
            cacheKey: CacheKeys.nazoratchiProfile,
            fromJsonFunction: (json) => UserProfile.fromJson(json),
          );
          emit(NazProfileError(
            message: 'Profil rasmini yangilashda va qayta yuklashda xatolik yuz berdi',
            cachedProfile: cachedProfile,
          ));

          if (cachedProfile != null) {
            emit(NazProfileLoaded(
              userProfile: cachedProfile,
              isFromCache: true,
              message: 'Keshdan eski profil rasmi qaytarildi',
            ));
          }
        }
      }
    } catch (e) {
      // Handle errors using SimpleErrorHandler
      final errorMessage = SimpleErrorHandler.handleError(e);

      // On exception, try to reload the original profile from server to get the old image URL
      try {
        final originalProfileResponse = await _remoteDatasource.getUserProfile(event.userId);

        if (originalProfileResponse.success && originalProfileResponse.userProfile != null) {
          // Cache the reloaded original profile
          await CacheHelper.cacheUserProfile<UserProfile>(
            cacheKey: CacheKeys.nazoratchiProfile,
            profile: originalProfileResponse.userProfile!,
            toJsonFunction: () => originalProfileResponse.userProfile!.toJson(),
          );

          // First emit error for UI feedback
          emit(NazProfileError(
            message: errorMessage,
            cachedProfile: originalProfileResponse.userProfile,
          ));

          // Then revert to loaded state with original profile (old image URL)
          emit(NazProfileLoaded(
            userProfile: originalProfileResponse.userProfile!,
            isFromCache: false,
            message: 'Eski profil rasmi qaytarildi',
          ));
        } else {
          // If can't reload from server, use cached profile as fallback
          final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
            cacheKey: CacheKeys.nazoratchiProfile,
            fromJsonFunction: (json) => UserProfile.fromJson(json),
          );
          emit(NazProfileError(
            message: errorMessage,
            cachedProfile: cachedProfile,
          ));

          if (cachedProfile != null) {
            emit(NazProfileLoaded(
              userProfile: cachedProfile,
              isFromCache: true,
              message: 'Keshdan eski profil rasmi qaytarildi',
            ));
          }
        }
      } catch (reloadError) {
        // If reload fails, use cached profile
        final cachedProfile = await CacheHelper.getCachedUserProfile<UserProfile>(
          cacheKey: CacheKeys.nazoratchiProfile,
          fromJsonFunction: (json) => UserProfile.fromJson(json),
        );
        emit(NazProfileError(
          message: errorMessage,
          cachedProfile: cachedProfile,
        ));

        if (cachedProfile != null) {
          emit(NazProfileLoaded(
            userProfile: cachedProfile,
            isFromCache: true,
            message: 'Keshdan eski profil rasmi qaytarildi',
          ));
        }
      }
    }
  }

  /// Override network connectivity check
  @override
  Future<bool> isNetworkConnected() async {
    return await _networkInfo.isConnected;
  }
}
