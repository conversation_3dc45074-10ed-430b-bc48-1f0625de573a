import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../core/utils/app_constants.dart';

class NazProfileMenuItem extends StatelessWidget {
  final String title;
  final String subTitle;
  final String? iconPath;
  final IconData? iconData;
  final VoidCallback? onTap;
  final bool showArrow;
  final Widget? trailing;
  final bool isLogout;
  final bool doNotColorFilter;

  const NazProfileMenuItem({
    super.key,
    required this.title,
    required this.subTitle,
    this.iconPath,
    this.iconData,
    this.onTap,
    this.showArrow = true,
    this.trailing,
    this.isLogout = false,
    this.doNotColorFilter = false,
  }) : assert(iconPath != null || iconData != null, 'Either iconPath or iconData must be provided');

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(cRadius12.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Row(
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(cRadius8.r),
              ),
              child: Center(
                child: iconPath != null
                    ? SvgPicture.asset(
                        iconPath!,
                        width: 20.w,
                        height: 20.w,
                        colorFilter: doNotColorFilter?ColorFilter.mode(Theme.of(context).colorScheme.onSurfaceVariant, BlendMode.dstIn):ColorFilter.mode(
                          isLogout ? AppColors.cReddishColor : Theme.of(context).colorScheme.onSurfaceVariant,
                          BlendMode.srcIn,
                        ),
                      )
                    : Icon(
                        iconData!,
                        size: 20.w,
                        color: isLogout ? AppColors.cReddishColor : Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
              ),
            ),
            Gap(16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: isLogout ? AppColors.cReddishColor : Theme.of(context).colorScheme.onSurface,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subTitle,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontSize: 12.sp,
                    ),
                  ),
                ],
              ),
            ),
            if (trailing != null)
              trailing!
            else if (showArrow)
              Icon(
                Icons.chevron_right,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                size: 20.w,
              ),
          ],
        ),
      ),
    );
  }
}
