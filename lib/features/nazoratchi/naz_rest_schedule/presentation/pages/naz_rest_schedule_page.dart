import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../widgets/rest_schedule_date_picker.dart';
import '../widgets/rest_schedule_note_input.dart';

class NazRestSchedulePage extends StatefulWidget {
  const NazRestSchedulePage({super.key});

  @override
  State<NazRestSchedulePage> createState() => _NazRestSchedulePageState();
}

class _NazRestSchedulePageState extends State<NazRestSchedulePage> {
  final TextEditingController _noteController = TextEditingController();
  DateTime _selectedDate = DateTime.now();

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.cBackgroundColor,
      appBar: AppBar(
        backgroundColor: AppColors.cBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: AppColors.white,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Dam olishni belgilash',
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.white,
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date Picker Section
                  RestScheduleDatePicker(
                    selectedDate: _selectedDate,
                    onDateSelected: (date) {
                      setState(() {
                        _selectedDate = date;
                      });
                    },
                  ),
                  Gap(24.h),

                  // Note Input Section
                  RestScheduleNoteInput(
                    controller: _noteController,
                  ),
                ],
              ),
            ),
            
            // Submit Button
            _buildSubmitButton(),
            Gap(MediaQuery.of(context).viewInsets.bottom),
          ],
        ),
      ),
    );
  }



  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 56.h,
      child: ElevatedButton(
        onPressed: _submitRestSchedule,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.cFirstColor,
          foregroundColor: AppColors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        child: Text(
          'Yuborish',
          style: AppTextStyles.buttonText.copyWith(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }



  void _submitRestSchedule() {
    if (_noteController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Iltimos, izoh yozing'),
          backgroundColor: AppColors.cReddishColor,
        ),
      );
      return;
    }

    // Handle rest schedule submission
    // You can add your logic here to save the rest schedule
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Dam olish muvaffaqiyatli belgilandi'),
        backgroundColor: AppColors.cGreenishColor,
      ),
    );
    
    Navigator.pop(context);
  }
}
