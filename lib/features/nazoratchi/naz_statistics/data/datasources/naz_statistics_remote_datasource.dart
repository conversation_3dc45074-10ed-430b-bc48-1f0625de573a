import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:dio/dio.dart';
import '../models/plan_statistics_model.dart';
import '../models/place_statistics_model.dart';
import '../models/payment_statistics_model.dart';

/// Remote datasource for statistics API calls
class NazStatisticsRemoteDatasourceImpl {
  final NetworkInfo networkInfo;
  final Dio dio;

  NazStatisticsRemoteDatasourceImpl({
    required this.networkInfo,
    required this.dio,
  });

  /// Fetch plan statistics for a specific date
  Future<PlanStatisticsModel> getPlanStatistics(String date) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.planStatisticsPath),
        type: DioExceptionType.connectionError,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
      );
    }

    try {
      final response = await dio.get(
        ApiPath.planStatisticsPath,
        queryParameters: {'date': date},
      );

      if (response.statusCode == 200 && response.data != null) {
        return PlanStatisticsModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.planStatisticsPath),
          type: DioExceptionType.badResponse,
          message: 'Plan statistikasini yuklashda xatolik yuz berdi',
        );
      }
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.planStatisticsPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Fetch place statistics for a specific date
  Future<PlaceStatisticsModel> getPlaceStatistics(String date) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.placeStatisticsPath),
        type: DioExceptionType.connectionError,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
      );
    }

    try {
      final response = await dio.get(
        ApiPath.placeStatisticsPath,
        queryParameters: {'date': date},
      );

      if (response.statusCode == 200 && response.data != null) {
        return PlaceStatisticsModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.placeStatisticsPath),
          type: DioExceptionType.badResponse,
          message: 'Joy statistikasini yuklashda xatolik yuz berdi',
        );
      }
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.placeStatisticsPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Fetch payment statistics (no date parameter needed)
  Future<PaymentStatisticsModel> getPaymentStatistics() async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.paymentStatisticsPath),
        type: DioExceptionType.connectionError,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
      );
    }

    try {
      final response = await dio.get(ApiPath.paymentStatisticsPath);

      if (response.statusCode == 200 && response.data != null) {
        return PaymentStatisticsModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.paymentStatisticsPath),
          type: DioExceptionType.badResponse,
          message: 'To\'lov statistikasini yuklashda xatolik yuz berdi',
        );
      }
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.paymentStatisticsPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Fetch all statistics for a specific date
  Future<Map<String, dynamic>> getAllStatistics(String date) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: 'statistics'),
        type: DioExceptionType.connectionError,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
      );
    }

    try {
      // Fetch all statistics in parallel
      final results = await Future.wait([
        getPlanStatistics(date),
        getPlaceStatistics(date),
        getPaymentStatistics(),
      ]);

      return {
        'planStatistics': results[0] as PlanStatisticsModel,
        'placeStatistics': results[1] as PlaceStatisticsModel,
        'paymentStatistics': results[2] as PaymentStatisticsModel,
      };
    } catch (e) {
      rethrow;
    }
  }
}
