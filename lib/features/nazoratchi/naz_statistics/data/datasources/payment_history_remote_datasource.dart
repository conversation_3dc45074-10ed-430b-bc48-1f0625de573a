import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:dio/dio.dart';
import '../models/payment_history_model.dart';

/// Remote datasource for naz payment history API calls
class NazPaymentHistoryRemoteDatasourceImpl {
  final NetworkInfo networkInfo;
  final Dio dio;

  NazPaymentHistoryRemoteDatasourceImpl({
    required this.networkInfo,
    required this.dio,
  });

  /// Fetch payment history with pagination and filters
  Future<PaymentHistoryResponse> getPaymentHistory({
    required int page,
    required int limit,
    int? paymentType,
    bool? payment,
    String? date,
  }) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.dailyPaymentHistoryPath),
        type: DioExceptionType.connectionError,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
      );
    }

    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'page': page,
        'limit': limit,
      };

      // Add optional parameters if they are not null
      if (paymentType != null) {
        queryParameters['paymentType'] = paymentType;
      }
      if (payment != null) {
        queryParameters['payment'] = payment;
      }
      if (date != null && date.isNotEmpty) {
        queryParameters['date'] = date;
      }

      final response = await dio.get(
        ApiPath.dailyPaymentHistoryPath,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200 && response.data != null) {
        return PaymentHistoryResponse.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.dailyPaymentHistoryPath),
          type: DioExceptionType.badResponse,
          message: 'To\'lov tarixini yuklashda xatolik yuz berdi',
        );
      }
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.dailyPaymentHistoryPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }
}
