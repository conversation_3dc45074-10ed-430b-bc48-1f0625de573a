import 'package:bloc/bloc.dart';
import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/data/datasources/naz_statistics_remote_datasource.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/data/models/plan_statistics_model.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/data/models/place_statistics_model.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/data/models/payment_statistics_model.dart';
import 'package:equatable/equatable.dart';

part 'naz_statistics_event.dart';
part 'naz_statistics_state.dart';

/// BLoC for managing naz statistics state
class NazStatisticsBloc extends Bloc<NazStatisticsEvent, NazStatisticsState>
    with ErrorHandlerMixin<NazStatisticsEvent, NazStatisticsState> {
  final NetworkInfo networkInfo;
  final NazStatisticsRemoteDatasourceImpl nazStatisticsRemoteDatasource;

  NazStatisticsBloc({
    required this.networkInfo,
    required this.nazStatisticsRemoteDatasource,
  }) : super(const NazStatisticsState()) {
    on<LoadPlanStatisticsEvent>(_handleLoadPlanStatistics);
    on<LoadPlaceStatisticsEvent>(_handleLoadPlaceStatistics);
    on<LoadPaymentStatisticsEvent>(_handleLoadPaymentStatistics);
    on<LoadAllStatisticsEvent>(_handleLoadAllStatistics);
    on<RefreshAllStatisticsEvent>(_handleRefreshAllStatistics);
  }

  /// Handle load plan statistics event
  Future<void> _handleLoadPlanStatistics(
    LoadPlanStatisticsEvent event,
    Emitter<NazStatisticsState> emit,
  ) async {
    await executeApiCall<PlanStatisticsModel>(
      apiCall: () => nazStatisticsRemoteDatasource.getPlanStatistics(event.date),
      onLoading: () => emit(state.copyWith(
        status: NazStatisticsStatus.loading,
        currentDate: event.date,
        errorType: null,
        isRefreshing: false,
        hasRefreshError: false,
      )),
      onSuccess: (planStatistics) {
        emit(state.copyWith(
          status: NazStatisticsStatus.success,
          planStatistics: planStatistics,
          message: '',
          errorType: null,
          isRefreshing: false,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) => emit(state.copyWith(
        status: NazStatisticsStatus.failure,
        message: message,
        errorType: _determineErrorType(message),
        isRefreshing: false,
        hasRefreshError: false,
      )),
    );
  }

  /// Handle load place statistics event
  Future<void> _handleLoadPlaceStatistics(
    LoadPlaceStatisticsEvent event,
    Emitter<NazStatisticsState> emit,
  ) async {
    await executeApiCall<PlaceStatisticsModel>(
      apiCall: () => nazStatisticsRemoteDatasource.getPlaceStatistics(event.date),
      onLoading: () => emit(state.copyWith(
        status: NazStatisticsStatus.loading,
        currentDate: event.date,
        errorType: null,
        isRefreshing: false,
        hasRefreshError: false,
      )),
      onSuccess: (placeStatistics) {
        emit(state.copyWith(
          status: NazStatisticsStatus.success,
          placeStatistics: placeStatistics,
          message: '',
          errorType: null,
          isRefreshing: false,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) => emit(state.copyWith(
        status: NazStatisticsStatus.failure,
        message: message,
        errorType: _determineErrorType(message),
        isRefreshing: false,
        hasRefreshError: false,
      )),
    );
  }

  /// Handle load payment statistics event
  Future<void> _handleLoadPaymentStatistics(
    LoadPaymentStatisticsEvent event,
    Emitter<NazStatisticsState> emit,
  ) async {
    await executeApiCall<PaymentStatisticsModel>(
      apiCall: () => nazStatisticsRemoteDatasource.getPaymentStatistics(),
      onLoading: () => emit(state.copyWith(
        status: NazStatisticsStatus.loading,
        errorType: null,
        isRefreshing: false,
        hasRefreshError: false,
      )),
      onSuccess: (paymentStatistics) {
        emit(state.copyWith(
          status: NazStatisticsStatus.success,
          paymentStatistics: paymentStatistics,
          message: '',
          errorType: null,
          isRefreshing: false,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) => emit(state.copyWith(
        status: NazStatisticsStatus.failure,
        message: message,
        errorType: _determineErrorType(message),
        isRefreshing: false,
        hasRefreshError: false,
      )),
    );
  }

  /// Handle load all statistics event
  Future<void> _handleLoadAllStatistics(
    LoadAllStatisticsEvent event,
    Emitter<NazStatisticsState> emit,
  ) async {
    await executeApiCall<Map<String, dynamic>>(
      apiCall: () => nazStatisticsRemoteDatasource.getAllStatistics(event.date),
      onLoading: () => emit(state.copyWith(
        status: NazStatisticsStatus.loading,
        currentDate: event.date,
        errorType: null,
        isRefreshing: false,
        hasRefreshError: false,
      )),
      onSuccess: (allStatistics) {
        emit(state.copyWith(
          status: NazStatisticsStatus.success,
          planStatistics: allStatistics['planStatistics'] as PlanStatisticsModel,
          placeStatistics: allStatistics['placeStatistics'] as PlaceStatisticsModel,
          paymentStatistics: allStatistics['paymentStatistics'] as PaymentStatisticsModel,
          message: '',
          errorType: null,
          isRefreshing: false,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) => emit(state.copyWith(
        status: NazStatisticsStatus.failure,
        message: message,
        errorType: _determineErrorType(message),
        isRefreshing: false,
        hasRefreshError: false,
      )),
    );
  }

  /// Handle refresh all statistics event
  Future<void> _handleRefreshAllStatistics(
    RefreshAllStatisticsEvent event,
    Emitter<NazStatisticsState> emit,
  ) async {
    // Set refreshing flag and keep existing data
    emit(state.copyWith(
      isRefreshing: true,
      errorType: null,
      hasRefreshError: false,
    ));

    // Check network connectivity first for refresh operations
    if (!await isNetworkConnected()) {
      // If no network during refresh, emit network error but keep existing data
      emit(state.copyWith(
        status: state.hasAnyStatistics ? NazStatisticsStatus.success : NazStatisticsStatus.failure,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
        errorType: NazStatisticsErrorType.network,
        isRefreshing: false,
        hasRefreshError: true,
      ));
      return;
    }

    // Attempt to load fresh data
    await executeApiCall<Map<String, dynamic>>(
      apiCall: () => nazStatisticsRemoteDatasource.getAllStatistics(event.date),
      onLoading: () {
        // Don't change status to loading during refresh to keep existing content visible
      },
      onSuccess: (allStatistics) {
        emit(state.copyWith(
          status: NazStatisticsStatus.success,
          planStatistics: allStatistics['planStatistics'] as PlanStatisticsModel,
          placeStatistics: allStatistics['placeStatistics'] as PlaceStatisticsModel,
          paymentStatistics: allStatistics['paymentStatistics'] as PaymentStatisticsModel,
          message: '',
          errorType: null,
          isRefreshing: false,
          hasRefreshError: false,
          currentDate: event.date,
        ));
      },
      onFailure: (message) {
        // Keep existing data and just set error info for toast
        emit(state.copyWith(
          status: state.hasAnyStatistics ? NazStatisticsStatus.success : NazStatisticsStatus.failure,
          message: message,
          errorType: _determineErrorType(message),
          isRefreshing: false,
          hasRefreshError: true,
        ));
      },
    );
  }

  /// Override network connectivity check
  @override
  Future<bool> isNetworkConnected() async {
    return await networkInfo.isConnected;
  }

  /// Determine error type based on error message
  NazStatisticsErrorType _determineErrorType(String message) {
    if (message.contains('Internet aloqasi yo\'q') ||
        message.contains('ulanishni tekshiring') ||
        message.contains('connection') ||
        message.contains('network')) {
      return NazStatisticsErrorType.network;
    }
    return NazStatisticsErrorType.api;
  }
}
