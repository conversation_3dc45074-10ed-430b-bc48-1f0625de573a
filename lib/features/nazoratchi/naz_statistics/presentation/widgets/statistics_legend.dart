import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';

class StatisticsLegend extends StatelessWidget {
  final int? successCount;
  final int? failureCount;
  final int? bothCount;
  final int? belgilanmaganCount;

  const StatisticsLegend({
    super.key,
    required this.successCount,
    required this.failureCount,
    required this.bothCount,
    required this.belgilanmaganCount,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _LegendItem(
          color: AppColors.cGreenishColor,
          title: "To'langan rastalar",
          count: successCount,
        ),
        const SizedBox(height: 12),
        _LegendItem(
          color: AppColors.cReddishColor,
          title: "To'lanmagan rastalar",
          count: failureCount,
        ),
        const SizedBox(height: 12),
        _LegendItem(
          color: AppColors.cYellowishColor,
          title: "Bo'sh rastalar",
          count: belgilanmaganCount,
        ),
        const SizedBox(height: 12),
        _LegendItem(
          color: Color(0xFF313334),
          title: "Belgilanmagan rastalar",
          count: bothCount,
        ),
      ],
    );
  }
}

class _LegendItem extends StatelessWidget {
  final Color color;
  final String title;
  final int? count;

  const _LegendItem({
    required this.color,
    required this.title,
    required this.count,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(2.r)
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        Text(
          count.toString(),
          style: AppTextStyles.titleMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
