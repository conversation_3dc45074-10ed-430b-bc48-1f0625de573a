import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/utils/app_functions.dart';
import '../pages/statistics_history_page.dart';
import 'statistics_card.dart';

class StatisticsMetricsRow extends StatelessWidget {
  final int? rejda;
  final int? tushgan;
  final int? clickOrqali;
  final int? naqdPul;
  final int? terminalOrqali;
  final int? qarzdalik;
  final String date;


  const StatisticsMetricsRow({
    super.key,
    required this.rejda,
    required this.tushgan,
    required this.clickOrqali,
    required this.naqdPul,
    required this.terminalOrqali,
    required this.qarzdalik,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // First row - Rej<PERSON> and <PERSON><PERSON><PERSON>
        IntrinsicHeight(
          child: Row(
            children: [
              StatisticsMetricCard(
                title: '<PERSON><PERSON><PERSON>',
                value: AppFunctions.formatNumberSafe(rejda),
                backgroundColor: AppColors.cFirstColor.withValues(alpha: 0.1),
                mainTextColor: AppColors.cFirstColor,
                icon: Assets.iconsRejada,
                iconColor: AppColors.cFirstColor,
                onTap: () {},
              ),
              const SizedBox(width: 12),
              StatisticsMetricCard(
                title: 'Tushgan',
                value: AppFunctions.formatNumberSafe(tushgan),
                backgroundColor:
                    AppColors.cGreenishColor.withValues(alpha: 0.1),
                mainTextColor: AppColors.cGreenishColor,
                icon: Assets.iconsTushgan,
                iconColor: AppColors.cGreenishColor,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => StatisticsHistoryPage(
                            title: 'Tushgan',
                            date: date,
                            paymentType: null,
                            payment: null,
                          )));
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        IntrinsicHeight(
          child: Row(
            children: [
              StatisticsMetricCard(
                title: 'Click orqali',
                value: AppFunctions.formatNumberSafe(clickOrqali),
                backgroundColor: Theme.of(context).colorScheme.surface,
                icon: Assets.iconsClick,
                mainTextColor: Theme.of(context).colorScheme.onSurface,
                iconColor: AppColors.cFirstColor,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => StatisticsHistoryPage(
                            title: 'Click orqali',
                            date: date,
                            paymentType: 3,
                            payment: null,
                          )));
                },
              ),
              const SizedBox(width: 12),
              StatisticsMetricCard(
                title: 'Naqd pul',
                value: AppFunctions.formatNumberSafe(naqdPul),
                backgroundColor: Theme.of(context).colorScheme.surface,
                icon: Assets.iconsWallet,
                mainTextColor: Theme.of(context).colorScheme.onSurface,
                iconColor: AppColors.cGreenishColor,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => StatisticsHistoryPage(
                            title: 'Naqd pul',
                            date: date,
                            paymentType: 1,
                            payment: null,
                          )));
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        IntrinsicHeight(
          child: Row(
            children: [
              StatisticsMetricCard(
                title: 'Terminal orqali',
                value: AppFunctions.formatNumberSafe(terminalOrqali),
                backgroundColor:
                    AppColors.cYellowishColor.withValues(alpha: 0.2),
                mainTextColor: Theme.of(context).colorScheme.onSurface,
                icon: Assets.iconsCardPos,
                iconColor: AppColors.cYellowishColor,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => StatisticsHistoryPage(
                            title: 'Terminal orqali',
                            date: date,
                            paymentType: 2,
                            payment: null,
                          )));
                },
              ),
              const SizedBox(width: 12),
              StatisticsMetricCard(
                title: 'Qarzdalik',
                value: AppFunctions.formatNumberSafe(qarzdalik),
                backgroundColor: AppColors.cReddishColor.withValues(alpha: 0.1),
                mainTextColor: AppColors.cReddishColor,
                icon: Assets.iconsQarzdorlik,
                iconColor: AppColors.cReddishColor,
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (_) => StatisticsHistoryPage(
                            title: 'Qarzdalik',
                            date: date,
                            paymentType: null,
                            payment: false,
                          )));
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
