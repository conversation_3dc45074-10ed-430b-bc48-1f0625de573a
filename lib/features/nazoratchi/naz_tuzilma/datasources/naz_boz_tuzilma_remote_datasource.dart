import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import '../models/pavilion_model.dart';

/// Abstract class for naz boz tuzilma remote data source
abstract class NazBozTuzilmaRemoteDatasource {
  Future<List<PavilionResponse>> getPavilions();
}

/// Implementation of naz boz tuzilma remote data source
class NazBozTuzilmaRemoteDatasourceImpl implements NazBozTuzilmaRemoteDatasource {
  final Dio _dio;
  final NetworkInfo _networkInfo;
  final GetStorage _storage;

  NazBozTuzilmaRemoteDatasourceImpl({
    required Dio dio,
    required NetworkInfo networkInfo,
    required GetStorage storage,
  })  : _dio = dio,
        _networkInfo = networkInfo,
        _storage = storage;

  @override
  Future<List<PavilionResponse>> getPavilions() async {
    // Check network connectivity first
    if (!await _networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.pavilionPath),
        type: DioExceptionType.connectionError,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
      );
    }

    try {
      // Get user ID from storage
      final userId = _storage.read(USER_ID) as String?;

      final response = await _dio.get(
        ApiPath.pavilionPath,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
        queryParameters: {
          "supervisor": userId ?? ""
        }
      );

      if (response.statusCode == 200 && response.data != null) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data.map((json) => PavilionResponse.fromJson(json)).toList();
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.pavilionPath),
          type: DioExceptionType.badResponse,
          message: 'Pavilion ma\'lumotlarini yuklashda xatolik yuz berdi',
        );
      }
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.pavilionPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }
}
