import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:dio/dio.dart';
import '../models/pavilion_model.dart';

/// Abstract class for naz pavilion remote data source
abstract class NazPavilionRemoteDatasource {
  Future<List<BlockData>> getBlocks(String pavilionId);
}

/// Implementation of naz pavilion remote data source
class NazPavilionRemoteDatasourceImpl implements NazPavilionRemoteDatasource {
  final Dio _dio;
  final NetworkInfo _networkInfo;

  NazPavilionRemoteDatasourceImpl({
    required Dio dio,
    required NetworkInfo networkInfo,
  })  : _dio = dio,
        _networkInfo = networkInfo;

  @override
  Future<List<BlockData>> getBlocks(String pavilionId) async {
    // Check network connectivity first
    if (!await _networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: 'mobile/place/block'),
        type: DioExceptionType.connectionError,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
      );
    }

    try {
      final response = await _dio.get(
        'mobile/place/block',
        queryParameters: {
          'pavilion': pavilionId,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data.map((json) => BlockData.fromJson(json)).toList();
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: 'mobile/place/block'),
          type: DioExceptionType.badResponse,
          message: 'Bloklar ma\'lumotlarini yuklashda xatolik yuz berdi',
        );
      }
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: 'mobile/place/block'),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }
}
