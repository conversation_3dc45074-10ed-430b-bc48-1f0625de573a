import 'dart:io';

import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/features/face_control/facedetectionview.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:page_transition/page_transition.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../di/dependency_injection.dart';
import '../models/payment_models.dart';
import '../services/payment_service.dart';
import '../bloc/rastalar_api/rastalar_api_bloc.dart';
import 'qr_payment_bottomsheet.dart';

/// Payment status bottomsheet that handles the entire payment flow
class PaymentStatusBottomSheet extends StatefulWidget {
  final PaymentType paymentType;
  final int quantity;
  final int dailyRate;
  final String sellerId;
  final String day;
  final List<String> placeIds;
  final String squareNumber;
  final String? pavilionId; // Add pavilion ID for proper refresh
  final VoidCallback onSuccess;
  final VoidCallback onCancel;

  const PaymentStatusBottomSheet({
    super.key,
    required this.paymentType,
    required this.quantity,
    required this.dailyRate,
    required this.sellerId,
    required this.day,
    required this.placeIds,
    required this.squareNumber,
    this.pavilionId,
    required this.onSuccess,
    required this.onCancel,
  });

  @override
  State<PaymentStatusBottomSheet> createState() =>
      _PaymentStatusBottomSheetState();
}

class _PaymentStatusBottomSheetState extends State<PaymentStatusBottomSheet> {
  final PaymentService _paymentService = di<PaymentService>();

  PaymentStatus _status = PaymentStatus.creating;
  String? _errorMessage;
  int? _paymentId;
  String? _paymentStringId;
  bool _isPendingPayment = false; // Track if this is a pending payment (201)
  CreatePaymentResponse? _paymentData; // Store full payment data

  @override
  void initState() {
    super.initState();
    _createPayment();
  }

  /// Create payment
  Future<void> _createPayment() async {
    try {
      setState(() {
        _status = PaymentStatus.creating;
        _errorMessage = null;
      });

      final result = await _paymentService.createPayment(
        seller: widget.sellerId,
        days: widget.quantity,
        price: widget.quantity * widget.dailyRate,
        paymentType: widget.paymentType,
        places: widget.placeIds,
      );

      if (result.isSuccess && result.data != null) {
        setState(() {
          _paymentId = result.data!.paymentId;
          _paymentStringId = result.data!.id;
          _paymentData = result.data!;
          _isPendingPayment =
              result.statusCode == 201; // 201 means pending payment
          _status = PaymentStatus.awaitingConfirmation;
        });

        // For QR payments with NEW payment (200), show QR code directly
        if (widget.paymentType == PaymentType.qr && result.statusCode == 200) {
          _proceedToQRPayment();
          return;
        }

        // For QR payments with old cheques (201), show cheque details with QR button
        // This will be handled in the UI with "QR kodni ko'rsatish" button
      } else {
        String errorMessage =
            result.errorMessage ?? 'To\'lov yaratishda xatolik';

        // Check for specific error about unconfirmed payment
        if (errorMessage.contains('tasdiqlanmagan to\'lov mavjud') ||
            errorMessage.contains('unconfirmed payment')) {
          errorMessage = 'Sizda tasdiqlanmagan to\'lov mavjud.\n\n'
              'Avval mavjud to\'lovni tasdiqlang yoki bekor qiling, '
              'keyin yangi to\'lov yarating.';
        }

        setState(() {
          _status = PaymentStatus.error;
          _errorMessage = errorMessage;
        });
      }
    } catch (e) {
      setState(() {
        _status = PaymentStatus.error;
        _errorMessage = 'To\'lov yaratishda xatolik: $e';
      });
    }
  }

  /// Confirm payment
  Future<void> _confirmPayment() async {
    if (Platform.isAndroid) {
      await Navigator.push(
        context,
        PageTransition(
          alignment: Alignment.center,
          type: PageTransitionType.scale,
          child: FaceRecognitionView(
            debug: EMULATOR,
            getBackAfterRecognized: true,
            afterRecognized: () async {
              if (_paymentStringId == null) return;

              try {
                setState(() {
                  _status = PaymentStatus.confirming;
                });

                final result =
                    await _paymentService.confirmPayment(_paymentStringId!);

                if (result.isSuccess && result.data?.message == 'success') {
                  setState(() {
                    _status = PaymentStatus.success;
                  });

                  // Trigger grid refresh and auto close after success
                  _refreshGrid();
                  Future.delayed(const Duration(seconds: 2), () {
                    if (mounted) {
                      widget.onSuccess();
                    }
                  });
                } else {
                  setState(() {
                    _status = PaymentStatus.error;
                    _errorMessage =
                        result.errorMessage ?? 'To\'lovni tasdiqlashda xatolik';
                  });
                }
              } catch (e) {
                setState(() {
                  _status = PaymentStatus.error;
                  _errorMessage = 'To\'lov jarayonida xatolik: $e';
                });
              }
            },
          ),
        ),
      ).then((value) {
        ///
      });
    } else if (Platform.isIOS) {
      ///
    }
  }

  /// Proceed to QR payment (for QR payments with old cheques)
  void _proceedToQRPayment() {
    if (_paymentStringId == null) return;

    // Close current bottomsheet
    Navigator.of(context).pop();

    // Show QR payment bottomsheet
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (qrContext) => QRPaymentBottomSheet(
        amount:
            '${widget.quantity} kunlik (${widget.quantity * widget.dailyRate} UZS)',
        squareNumber: int.tryParse(widget.squareNumber) ?? 0,
        sellerId: widget.sellerId,
        day: widget.day,
        price: widget.quantity * widget.dailyRate,
        quantity: widget.quantity,
        placeIds: widget.placeIds,
        onPaymentSuccess: () {
          Navigator.of(qrContext).pop();
          widget.onSuccess();
        },
        onPaymentError: () {
          Navigator.of(qrContext).pop();
          // Return to payment selector
        },
        onClose: () {
          Navigator.of(qrContext).pop();
          // Return to payment selector
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          const SizedBox(height: 24),

          // Title
          Text(
            _getTitle(),
            style: AppTextStyles.titleLarge.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: 40),

          // Content based on status
          _buildContent(),

          const SizedBox(height: 40),

          // Action buttons
          _buildActionButtons(),

          const SizedBox(height: 26),
        ],
      ),
    );
  }

  String _getTitle() {
    switch (_status) {
      case PaymentStatus.creating:
        return 'To\'lov yaratilmoqda...';
      case PaymentStatus.awaitingConfirmation:
        if (_isPendingPayment) {
          return 'Mavjud to\'lov topildi';
        } else {
          return 'To\'lov yaratildi';
        }
      case PaymentStatus.confirming:
        return 'To\'lov tasdiqlanmoqda...';
      case PaymentStatus.success:
        return 'To\'landi!';
      case PaymentStatus.error:
        return 'Xatolik yuz berdi';
    }
  }

  Widget _buildContent() {
    switch (_status) {
      case PaymentStatus.creating:
      case PaymentStatus.confirming:
        return Center(
          child: SizedBox(
            width: 60,
            height: 60,
            child: CircularProgressIndicator(
              color: Theme.of(context).colorScheme.primary,
              strokeWidth: 4,
            ),
          ),
        );

      case PaymentStatus.awaitingConfirmation:
        return Column(
          children: [
            Icon(
              widget.paymentType == PaymentType.cash
                  ? Icons.payments_outlined
                  : widget.paymentType == PaymentType.qr
                      ? Icons.qr_code
                      : Icons.credit_card_outlined,
              size: 60,
              color: _isPendingPayment ? Colors.orange : Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),

            // Show cheque details
            if (_paymentData != null) ...[
              _buildChequeDetails(),
              const SizedBox(height: 16),
            ],

            Text(
              '${widget.quantity} kunlik (${widget.quantity * widget.dailyRate} UZS)',
              style: AppTextStyles.bodyLarge.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Joylar: ${widget.squareNumber}',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),

            if (_isPendingPayment) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  'Bu to\'lov avval yaratilgan va hali tasdiqlanmagan',
                  textAlign: TextAlign.center,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.orange,
                  ),
                ),
              ),
            ],
          ],
        );

      case PaymentStatus.success:
        return SvgPicture.asset(Assets.iconsTickCircle, height: 80);

      case PaymentStatus.error:
        return Column(
          children: [
            const Icon(
              Icons.error_outline,
              size: 60,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage ?? 'Noma\'lum xatolik',
              textAlign: TextAlign.center,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        );
    }
  }

  Widget _buildActionButtons() {
    switch (_status) {
      case PaymentStatus.creating:
      case PaymentStatus.confirming:
        return TextButton(
          onPressed: widget.onCancel,
          child: Text(
            'Bekor qilish',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        );

      case PaymentStatus.awaitingConfirmation:
        if (_isPendingPayment) {
          // For pending payments, show ignore/delete and confirm options
          return Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _deletePendingPayment,
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.red),
                      ),
                      child: Text(
                        'O\'chirish',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: widget.paymentType == PaymentType.qr
                          ? _proceedToQRPayment
                          : _confirmPayment,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                      ),
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          widget.paymentType == PaymentType.qr
                              ? 'QR kodni ko\'rsatish'
                              : 'Qabul qildim',
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Theme.of(context).colorScheme.onPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: widget.onCancel,
                  child: Text(
                    'Bekor qilish',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ),
            ],
          );
        } else {
          // For new payments, show normal confirm/cancel options
          return Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: widget.onCancel,
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Theme.of(context).colorScheme.onSurfaceVariant),
                  ),
                  child: Text(
                    'Bekor qilish',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _confirmPayment,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                  ),
                  child: Text(
                    'Qabul qildim',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          );
        }

      case PaymentStatus.success:
        return ElevatedButton(
          onPressed: widget.onSuccess,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
          ),
          child: Text(
            'Davom etish',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        );

      case PaymentStatus.error:
        return Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: widget.onCancel,
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Theme.of(context).colorScheme.onSurfaceVariant),
                ),
                child: Text(
                  'Yopish',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: _createPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                ),
                child: Text(
                  'Qayta urinish',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        );
    }
  }

  /// Handle deleting pending payment and return to payment selector
  Future<void> _deletePendingPayment() async {
    if (_paymentId == null) return;

    try {
      setState(() {
        _status = PaymentStatus.creating; // Show loading state
      });

      // Delete the old cheque instead of just ignoring
      final result = await _paymentService.deleteOldCheque(_paymentId!);

      if (result.isSuccess) {
        // Successfully deleted, go back to payment selector
        widget.onCancel();
      } else {
        setState(() {
          _status = PaymentStatus.error;
          _errorMessage = result.errorMessage ?? 'Chekni o\'chirishda xatolik';
        });
      }
    } catch (e) {
      setState(() {
        _status = PaymentStatus.error;
        _errorMessage = 'Chekni o\'chirishda xatolik: $e';
      });
    }
  }

  /// Refresh the grid page after successful payment
  void _refreshGrid() {
    try {
      final bloc = GetIt.instance<RastalarApiBloc>();

      // Get the block ID from the current data
      String? blockId;
      if (widget.placeIds.isNotEmpty) {
        blockId = widget.placeIds.first; // Use the first place ID as block ID
      }

      bloc.add(RefreshSquares(
        blockId: blockId,
        pavilionId: widget.pavilionId,
      ));
      debugPrint(
          'Grid refresh triggered after payment success with blockId: $blockId, pavilionId: ${widget.pavilionId}');
    } catch (e) {
      debugPrint('Error triggering grid refresh: $e');
    }
  }

  /// Generate a readable check number from payment ID
  String _generateCheckNumber(String paymentId) {
    // Use the last 8 characters of the payment ID for better readability
    // If the ID is shorter than 8 characters, use the full ID
    if (paymentId.length >= 8) {
      return paymentId.substring(paymentId.length - 8).toUpperCase();
    } else {
      return paymentId.toUpperCase();
    }
  }

  /// Build cheque details widget
  Widget _buildChequeDetails() {
    if (_paymentData == null) return const SizedBox.shrink();

    final payment = _paymentData!;
    // Use the date field from API response (format: "2025-07-16 16:34")
    final dateStr = payment.date;
    String formattedDate = dateStr;
    String formattedTime = '';

    try {
      // Parse the date string to extract date and time
      if (dateStr.contains(' ')) {
        final parts = dateStr.split(' ');
        final datePart = parts[0]; // "2025-07-16"
        final timePart = parts.length > 1 ? parts[1] : ''; // "16:34"

        // Format date from "2025-07-16" to "16.07.2025"
        final dateComponents = datePart.split('-');
        if (dateComponents.length == 3) {
          formattedDate =
              '${dateComponents[2]}.${dateComponents[1]}.${dateComponents[0]}';
        }

        formattedTime = timePart;
      }
    } catch (e) {
      // If parsing fails, use the original date string
      formattedDate = dateStr;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Chek raqami:',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              GestureDetector(
                onTap: () {
                  // Copy check number to clipboard
                  final checkNumber = _generateCheckNumber(payment.paymentId.toString());
                  Clipboard.setData(ClipboardData(text: checkNumber));
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Chek raqami nusxalandi: $checkNumber'),
                      duration: const Duration(seconds: 2),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                    ),
                  );
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '#${_generateCheckNumber(payment.paymentId.toString())}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      Icons.copy,
                      size: 14,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Yaratilgan:',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                '$formattedDate $formattedTime',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Kunlar soni:',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              Text(
                '${payment.day} kun',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Holat:',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _isPendingPayment
                      ? Colors.orange.withValues(alpha: 0.2)
                      : Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _isPendingPayment ? 'Kutilmoqda' : 'Yangi',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: _isPendingPayment ? Colors.orange : Colors.green,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

enum PaymentStatus {
  creating,
  awaitingConfirmation,
  confirming,
  success,
  error,
}
