import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:screen_brightness/screen_brightness.dart';
import 'package:uzpay/uzpay.dart';
import 'package:uzpay/objects.dart';
import 'package:uzpay/enums.dart';
import 'loading_dialog.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../di/dependency_injection.dart';
import '../services/payment_service.dart';
import '../models/payment_models.dart';
import 'payment_result_dialog.dart';

/// QR Code payment bottomsheet widget
class QRPaymentBottomSheet extends StatefulWidget {
  final String amount;
  final int squareNumber;
  final String sellerId;
  final String day;
  final int price;
  final int quantity;
  final List<String> placeIds;
  final VoidCallback? onPaymentSuccess;
  final VoidCallback? onPaymentError;
  final VoidCallback? onClose;

  const QRPaymentBottomSheet({
    super.key,
    required this.amount,
    required this.squareNumber,
    required this.sellerId,
    required this.day,
    required this.price,
    required this.quantity,
    required this.placeIds,
    this.onPaymentSuccess,
    this.onPaymentError,
    this.onClose,
  });

  @override
  State<QRPaymentBottomSheet> createState() => _QRPaymentBottomSheetState();
}

class _QRPaymentBottomSheetState extends State<QRPaymentBottomSheet> {
  final PaymentService _paymentService = di<PaymentService>();

  bool _isLoading = true;
  bool _isCheckingPayment = false;
  bool _hasError = false;
  String _errorMessage = '';
  String? _paymentId;
  Uint8List? _qrImageBytes;

  @override
  void initState() {
    super.initState();
    _setMaxBrightness();
    _createPayment();
  }

  @override
  void dispose() {
    _restoreOriginalBrightness();
    super.dispose();
  }

  /// Set screen brightness to maximum when QR bottomsheet opens
  Future<void> _setMaxBrightness() async {
    try {
      // Set brightness to maximum (1.0)
      await ScreenBrightness().setApplicationScreenBrightness(1.0);
    } catch (e) {
      // Handle any errors silently
      debugPrint('Error setting brightness: $e');
    }
  }

  /// Restore original brightness when bottomsheet closes
  Future<void> _restoreOriginalBrightness() async {
    try {
      // Reset application brightness to follow system brightness
      await ScreenBrightness().resetApplicationScreenBrightness();
    } catch (e) {
      // Handle any errors silently
      debugPrint('Error restoring brightness: $e');
    }
  }

  /// Create payment and generate QR code
  Future<void> _createPayment() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // Create payment with paymentType: 3 (QR)
      final result = await _paymentService.createPayment(
        seller: widget.sellerId,
        days: widget.quantity,
        price: widget.price,
        paymentType: PaymentType.qr,
        places: widget.placeIds,
      );

      if (result.isSuccess && result.data != null) {
        _paymentId = result.data!.id;
        await _generateQRCode(_paymentId!);
      } else {
        setState(() {
          _hasError = true;
          _errorMessage = result.errorMessage ?? 'To\'lov yaratishda xatolik';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'To\'lov yaratishda xatolik: $e';
        _isLoading = false;
      });
    }
  }

  /// Generate QR code using UzPay package
  Future<void> _generateQRCode(String paymentId) async {
    try {
      // Load Click logo from assets
      final ByteData logoData = await rootBundle.load(Assets.iconsClick);
      final Uint8List logoBytes = logoData.buffer.asUint8List();

      // Configure Click parameters for QR generation
      final clickParams = ClickParams(
        serviceId: '31654', // Replace with your actual service ID
        merchantId: '16827', // Replace with your actual merchant ID
        merchantUserId: '31654', // Replace with your actual merchant user ID
        transactionParam: paymentId,
      );

      final params = Params(clickParams: clickParams);

      // Generate QR code with Click logo using UzPay
      final Uint8List qrBytes = await UzPay.generatePaymentQR(
        amount: widget.price.toDouble(),
        paymentSystem: PaymentSystem.Click,
        paymentParams: params,
        logoImage: logoBytes,
        logoSize: 80.0,
        logoBackgroundColor: Colors.white,
        logoPadding: 20.0,
        logoBackgroundRadius: 20.0, // 20px corner radius as specified
      );

      setState(() {
        _qrImageBytes = qrBytes;
        _isLoading = false;
        _hasError = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'QR kod yaratishda xatolik: $e';
        _isLoading = false;
      });
    }
  }

  /// Check payment status
  Future<void> _checkPaymentStatus() async {
    if (_paymentId == null) return;

    setState(() {
      _isCheckingPayment = true;
    });

    try {
      final result = await _paymentService.checkPaymentStatus(_paymentId!);

      if (result.isSuccess && result.data != null) {
        // Payment found and successful
        await _restoreOriginalBrightness();
        if (mounted) {
          Navigator.of(context).pop();
          widget.onPaymentSuccess?.call();
        }
      } else {
        // Handle 404 or other errors - show error overlay but keep bottom sheet open
        if (mounted) {
          _showErrorOverlay(result.errorMessage ?? 'To\'lov topilmadi');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorOverlay('To\'lov tekshirishda xatolik: $e');
      }
    } finally {
      setState(() {
        _isCheckingPayment = false;
      });
    }
  }

  /// Show error overlay dialog while keeping bottom sheet open
  void _showErrorOverlay(String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => PaymentResultDialog.error(
        message: message,
        onClose: () {
          Navigator.of(context).pop(); // Close only the error dialog
        },
      ),
    );
  }

  /// Retry payment creation
  void _retryPayment() {
    _createPayment();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          await _restoreOriginalBrightness();
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.only(bottom: 32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              const SizedBox(height: 24),

              // Title
              Text(
                'Click orqali to\'lash',
                style: AppTextStyles.titleLarge.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),

              const SizedBox(height: 8),

              // Square info
              Text(
                'Rasta #${widget.squareNumber}',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.cTextGrayColor,
                ),
              ),

              const SizedBox(height: 32),

              // Content based on state
              if (_isLoading) ...[
                // Loading state
                Container(
                  width: double.infinity,
                  height: 250.h,
                  alignment: Alignment.center,
                  child: const CircularProgressIndicator(
                    color: AppColors.cFirstColor,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'QR kod yaratilmoqda...',
                  textAlign: TextAlign.center,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ] else if (_hasError) ...[
                // Error state
                const SizedBox(
                  width: 200,
                  height: 200,
                  child: Center(
                    child: Icon(
                      Icons.error_outline,
                      size: 80,
                      color: AppColors.cReddishColor,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    _errorMessage,
                    textAlign: TextAlign.center,
                    style: AppTextStyles.titleLarge.copyWith(
                      color: AppColors.cReddishColor,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                // Retry button
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: _retryPayment,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.cFirstColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Qayta urinish',
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ] else ...[
                // Success state - show QR code
                Container(
                  width: 200,
                  height: 200,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.white, // QR code background should always be white for scanning
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 1,
                    ),
                  ),
                  child: _qrImageBytes != null
                      ? Image.memory(_qrImageBytes!)
                      : Image.asset(
                          Assets.imagesQrExample),
                ),
                const SizedBox(height: 24),
                Text(
                  'To\'lovni amalga oshirish\nuchun QR-codni skanerlang',
                  textAlign: TextAlign.center,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 32),
                // Payment check button
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed:
                          _isCheckingPayment ? null : _checkPaymentStatus,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.cFirstColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isCheckingPayment
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: AppColors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              'To\'lovni tekshirish',
                              style: AppTextStyles.bodyLarge.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ),
              ],

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}
