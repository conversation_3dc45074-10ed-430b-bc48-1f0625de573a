import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../models/rastalar_model.dart';

/// Dialog widget for empty/available squares
class RastalarEmptySquareDialog extends StatefulWidget {
  final RastalarSquare square;
  final GroupedSquares? groupedSquares; // For API-based squares

  const RastalarEmptySquareDialog({
    super.key,
    required this.square,
    this.groupedSquares,
  });

  /// Constructor for API-based grouped squares
  const RastalarEmptySquareDialog.fromGroupedSquares({
    super.key,
    required GroupedSquares groupedSquares,
  })  : square = const RastalarSquare(
            number: 0, status: SquareStatus.available, seller: null),
        groupedSquares = groupedSquares;

  @override
  State<RastalarEmptySquareDialog> createState() =>
      _RastalarEmptySquareDialogState();
}

class _RastalarEmptySquareDialogState extends State<RastalarEmptySquareDialog> {
  /// Check if we're using API-based data
  bool get _isApiMode => widget.groupedSquares != null;

  /// Get the appropriate title
  String get _displayTitle {
    if (_isApiMode) {
      return widget.groupedSquares!.displayTitle;
    }
    return 'Rasta #${widget.square.number}';
  }

  /// Get the appropriate tariff title
  String get _tariffTitle {
    if (_isApiMode) {
      return widget.groupedSquares!.tariffTitle;
    }
    return 'Ho\'l mevalar'; // Default for legacy
  }

  /// Get daily rate
  int get _dailyRate {
    if (_isApiMode && widget.groupedSquares!.squares.isNotEmpty) {
      return widget.groupedSquares!.squares.first
          .price; // Use main model price, not tariff price
    }
    return 12000; // Default for legacy
  }

  /// Get total square meters (for API mode)
  int _getTotalSquareMeters() {
    if (_isApiMode) {
      return widget.groupedSquares!.squares
          .fold(0, (sum, square) => sum + square.squareMeter);
    }
    return 3; // Default for legacy
  }

  /// Get photo URL from API data
  String? _getPhotoUrl() {
    if (_isApiMode && widget.groupedSquares!.squares.isNotEmpty) {
      return widget.groupedSquares!.squares.first.photo;
    }
    return null;
  }

  /// Get description from API data
  String _getDescription() {
    if (_isApiMode && widget.groupedSquares!.squares.isNotEmpty) {
      final desc = widget.groupedSquares!.squares.first.desc;
      if (desc != null && desc.isNotEmpty) {
        return desc;
      }
    }
    // Default description
    return 'Ushbu rasta hozirda bo\'sh. Savdo faoliyati olib borilmayapti. Ijaraga berish uchun ochiq';
  }

  /// Build image widget with network image or placeholder fallback
  Widget _buildImage() {
    final photoUrl = _getPhotoUrl();

    print(photoUrl);

    if (photoUrl != null && photoUrl.isNotEmpty) {
      return Image.network(
        ApiPath.baseUrlFile+photoUrl,
        height: 208,
        width: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          // Fallback to placeholder on error
          return Container(
            height: 208,
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(color: Theme.of(context).colorScheme.surfaceContainerHighest, borderRadius: BorderRadius.circular(12)),
            child: Icon(
              Icons.image_not_supported,size: 50,
            ),
          );
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            height: 208,
            width: double.infinity,
            color: Theme.of(context).colorScheme.surface,
            child: Center(
              child: CircularProgressIndicator(
                color: Theme.of(context).colorScheme.primary,
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            ),
          );
        },
      );
    } else {
      // Use placeholder when no photo URL
      return Container(
        height: 208,
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(color: Theme.of(context).colorScheme.surfaceContainerHighest, borderRadius: BorderRadius.circular(12)),
        child: Icon(
          Icons.image_not_supported,size: 50,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxDialogHeight = screenHeight * 0.85; // Use 85% of screen height

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Stack(
        children: [
          Container(
            constraints: BoxConstraints(
              maxHeight: maxDialogHeight,
            ),
            margin: const EdgeInsets.symmetric(vertical: 60),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _displayTitle,
                          style: AppTextStyles.titleLarge.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            'Bo\'sh',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Content
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Image from API or placeholder
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: _buildImage(),
                        ),

                        const SizedBox(height: 20),

                        // Tariff Info Widget
                        _buildTariffInfoWidget(),

                        const SizedBox(height: 20),

                        Divider(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)),
                        const SizedBox(height: 20),

                        // Description from API or default
                        Text(
                          _getDescription(),
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                            height: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
          // Close button
          Positioned(
            bottom: 0,
            right: 0,
            left: 0,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                width: 45,
                height: 45,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 25,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w400,
          ),
        ),
        Flexible(
          child: Text(
            textAlign: TextAlign.end,
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTariffInfoWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Kategoriya
          _buildDetailRow('Kategoriya:', _tariffTitle),
          const SizedBox(height: 8),

          // Tarif
          _buildDetailRow('Tarif:', '1 kunlik ($_dailyRate UZS)'),
          const SizedBox(height: 8),

          // Joy hajmi
          if (_isApiMode && widget.groupedSquares!.isMultiOccupant) ...[
            _buildDetailRow('Jami kvadrat:', '${_getTotalSquareMeters()} m²'),
          ] else if (_isApiMode) ...[
            _buildDetailRow('Joy hajmi:', '${_getTotalSquareMeters()} metr'),
          ] else ...[
            _buildDetailRow('Joy hajmi:', '3 metr'),
          ],
        ],
      ),
    );
  }
}
