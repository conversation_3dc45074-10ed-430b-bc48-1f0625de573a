import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../di/dependency_injection.dart';
import '../services/payment_service.dart';
import 'payment_result_dialog.dart';

/// Terminal payment confirmation dialog
class TerminalPaymentDialog extends StatefulWidget {
  final String amount;
  final int squareNumber;
  final String paymentId; // Payment ID from creation step
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  const TerminalPaymentDialog({
    super.key,
    required this.amount,
    required this.squareNumber,
    required this.paymentId,
    this.onConfirm,
    this.onCancel,
  });

  @override
  State<TerminalPaymentDialog> createState() => _TerminalPaymentDialogState();
}

class _TerminalPaymentDialogState extends State<TerminalPaymentDialog> {
  final PaymentService _paymentService = di<PaymentService>();

  bool _isConfirmingPayment = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 32),

            // Illustration - using card-pos icon for terminal
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: SvgPicture.asset(Assets.iconsCardPos),
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              '${widget.amount} uchun ${_formatAmount(widget.amount)}\nterminal orqali to\'landimi?',
              textAlign: TextAlign.center,
              style: AppTextStyles.titleMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
                height: 1.4,
              ),
            ),

            const SizedBox(height: 32),

            // Buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  // Confirm button
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: _isConfirmingPayment ? null : _handleConfirm,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.cGreenishColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isConfirmingPayment
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: AppColors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              'To\'landi',
                              style: AppTextStyles.bodyLarge.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Cancel button
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: TextButton(
                      onPressed: _isConfirmingPayment ? null : _handleCancel,
                      style: TextButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color:
                                AppColors.cTextGrayColor.withValues(alpha: 0.3),
                          ),
                        ),
                      ),
                      child: Text(
                        'Bekor qilish',
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.cTextGrayColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  /// Handle confirm button press - create payment and then confirm it
  Future<void> _handleConfirm() async {
    setState(() {
      _isConfirmingPayment = true;
    });

    try {
      // Confirm the payment using the provided payment ID
      final confirmResult =
          await _paymentService.confirmPayment(widget.paymentId);

      if (confirmResult.isSuccess) {
        // Success - close dialog and show success
        if (mounted) {
          Navigator.of(context).pop();
          widget.onConfirm?.call();
        }
      } else {
        _showErrorDialog(
            confirmResult.errorMessage ?? 'To\'lovni tasdiqlashda xatolik');
      }
    } catch (e) {
      _showErrorDialog('To\'lov jarayonida xatolik: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isConfirmingPayment = false;
        });
      }
    }
  }

  /// Handle cancel button press
  void _handleCancel() {
    Navigator.of(context).pop();
    widget.onCancel?.call();
  }

  /// Show error dialog
  void _showErrorDialog(String message) {
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => PaymentResultDialog.error(
          message: message,
          onClose: () {
            Navigator.of(context).pop(); // Close error dialog
          },
        ),
      );
    }
  }

  String _formatAmount(String amount) {
    // Extract number from amount string like "1 kunlik (12 000 UZS)"
    final regex = RegExp(r'\(([^)]+)\)');
    final match = regex.firstMatch(amount);
    if (match != null) {
      return match.group(1) ?? amount;
    }
    return amount;
  }
}
