import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'payment_models.g.dart';

/// Payment types enum
enum PaymentType {
  cash(1),
  terminal(2),
  qr(3);

  const PaymentType(this.value);
  final int value;
}

/// Request model for creating payment
@JsonSerializable()
class CreatePaymentRequest extends Equatable {
  final String seller;
  final int day;
  final int price;
  final int paymentType;
  final List<String> places;

  const CreatePaymentRequest({
    required this.seller,
    required this.day,
    required this.price,
    required this.paymentType,
    required this.places,
  });

  factory CreatePaymentRequest.fromJson(Map<String, dynamic> json) =>
      _$CreatePaymentRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CreatePaymentRequestToJson(this);

  @override
  List<Object?> get props => [seller, day, price, paymentType, places];
}

/// Response model for payment creation
@JsonSerializable()
class CreatePaymentResponse extends Equatable {
  @JsonKey(name: '_id')
  final String id;
  final String? province;
  final String? region;
  final String? market;
  final int price;
  final String? supervisor;
  final String seller;
  final String date; // Use date field instead of createdAt for display
  final int? paymentId;
  final int status;
  final int paymentType;
  final bool? checkBugalter;
  final bool? check;
  final dynamic clickData;
  final int day;
  final List<String> places;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CreatePaymentResponse({
    required this.id,
    this.province,
    this.region,
    this.market,
    required this.price,
    this.supervisor,
    required this.seller,
    required this.date,
    this.paymentId,
    required this.status,
    required this.paymentType,
    this.checkBugalter,
    this.check,
    this.clickData,
    required this.day,
    required this.places,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CreatePaymentResponse.fromJson(Map<String, dynamic> json) =>
      _$CreatePaymentResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CreatePaymentResponseToJson(this);

  @override
  List<Object?> get props => [
        id,
        province,
        region,
        market,
        price,
        supervisor,
        seller,
        date,
        paymentId,
        status,
        paymentType,
        checkBugalter,
        check,
        clickData,
        day,
        places,
        createdAt,
        updatedAt,
      ];
}

/// Response model for payment status check
@JsonSerializable()
class PaymentStatusResponse extends Equatable {
  @JsonKey(name: '_id')
  final String id;
  final String seller;
  final int day;
  final int price;
  final int paymentType;
  final List<String> places;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool? paid;

  const PaymentStatusResponse({
    required this.id,
    required this.seller,
    required this.day,
    required this.price,
    required this.paymentType,
    required this.places,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.paid,
  });

  factory PaymentStatusResponse.fromJson(Map<String, dynamic> json) =>
      _$PaymentStatusResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentStatusResponseToJson(this);

  @override
  List<Object?> get props => [
        id,
        seller,
        day,
        price,
        paymentType,
        places,
        status,
        createdAt,
        updatedAt,
        paid,
      ];
}

/// Response model for payment confirmation
@JsonSerializable()
class ConfirmPaymentResponse extends Equatable {
  final String message;

  const ConfirmPaymentResponse({
    required this.message,
  });

  factory ConfirmPaymentResponse.fromJson(Map<String, dynamic> json) =>
      _$ConfirmPaymentResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ConfirmPaymentResponseToJson(this);

  @override
  List<Object?> get props => [message];
}

/// Generic API response wrapper
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> extends Equatable {
  final bool success;
  final String? message;
  final T? data;
  final int? statusCode;

  const ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.statusCode,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  @override
  List<Object?> get props => [success, message, data, statusCode];
}

/// Payment error model
class PaymentError extends Equatable {
  final String message;
  final int? statusCode;
  final String? errorCode;

  const PaymentError({
    required this.message,
    this.statusCode,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, statusCode, errorCode];
}

/// Request model for place check
@JsonSerializable()
class PlaceCheckRequest extends Equatable {
  final String seller;

  const PlaceCheckRequest({
    required this.seller,
  });

  factory PlaceCheckRequest.fromJson(Map<String, dynamic> json) =>
      _$PlaceCheckRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PlaceCheckRequestToJson(this);

  @override
  List<Object?> get props => [seller];
}

/// Response model for place check
@JsonSerializable()
class PlaceCheckResponse extends Equatable {
  final bool success;
  final String message;
  final dynamic data;

  const PlaceCheckResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory PlaceCheckResponse.fromJson(Map<String, dynamic> json) {
    // Handle the actual API response format: {"message":"success"}
    final message = json['message'] ?? '';
    final isSuccess = message.toLowerCase() == 'success';

    return PlaceCheckResponse(
      success: isSuccess,
      message: message,
      data: json['data'],
    );
  }

  Map<String, dynamic> toJson() => _$PlaceCheckResponseToJson(this);

  @override
  List<Object?> get props => [success, message, data];
}
