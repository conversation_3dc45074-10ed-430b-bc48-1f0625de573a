// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreatePaymentRequest _$CreatePaymentRequestFromJson(
        Map<String, dynamic> json) =>
    CreatePaymentRequest(
      seller: json['seller'] as String,
      day: (json['day'] as num).toInt(),
      price: (json['price'] as num).toInt(),
      paymentType: (json['paymentType'] as num).toInt(),
      places:
          (json['places'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$CreatePaymentRequestToJson(
        CreatePaymentRequest instance) =>
    <String, dynamic>{
      'seller': instance.seller,
      'day': instance.day,
      'price': instance.price,
      'paymentType': instance.paymentType,
      'places': instance.places,
    };

CreatePaymentResponse _$CreatePaymentResponseFromJson(
        Map<String, dynamic> json) =>
    CreatePaymentResponse(
      id: json['_id'] as String,
      province: json['province'] as String?,
      region: json['region'] as String?,
      market: json['market'] as String?,
      price: (json['price'] as num).toInt(),
      supervisor: json['supervisor'] as String?,
      seller: json['seller'] as String,
      date: json['date'] as String,
      paymentId: (json['paymentId'] as num?)?.toInt(),
      status: (json['status'] as num).toInt(),
      paymentType: (json['paymentType'] as num).toInt(),
      checkBugalter: json['checkBugalter'] as bool?,
      check: json['check'] as bool?,
      clickData: json['clickData'],
      day: (json['day'] as num).toInt(),
      places:
          (json['places'] as List<dynamic>).map((e) => e as String).toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$CreatePaymentResponseToJson(
        CreatePaymentResponse instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'province': instance.province,
      'region': instance.region,
      'market': instance.market,
      'price': instance.price,
      'supervisor': instance.supervisor,
      'seller': instance.seller,
      'date': instance.date,
      'paymentId': instance.paymentId,
      'status': instance.status,
      'paymentType': instance.paymentType,
      'checkBugalter': instance.checkBugalter,
      'check': instance.check,
      'clickData': instance.clickData,
      'day': instance.day,
      'places': instance.places,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

PaymentStatusResponse _$PaymentStatusResponseFromJson(
        Map<String, dynamic> json) =>
    PaymentStatusResponse(
      id: json['_id'] as String,
      seller: json['seller'] as String,
      day: (json['day'] as num).toInt(),
      price: (json['price'] as num).toInt(),
      paymentType: (json['paymentType'] as num).toInt(),
      places:
          (json['places'] as List<dynamic>).map((e) => e as String).toList(),
      status: json['status'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      paid: json['paid'] as bool?,
    );

Map<String, dynamic> _$PaymentStatusResponseToJson(
        PaymentStatusResponse instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'seller': instance.seller,
      'day': instance.day,
      'price': instance.price,
      'paymentType': instance.paymentType,
      'places': instance.places,
      'status': instance.status,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'paid': instance.paid,
    };

ConfirmPaymentResponse _$ConfirmPaymentResponseFromJson(
        Map<String, dynamic> json) =>
    ConfirmPaymentResponse(
      message: json['message'] as String,
    );

Map<String, dynamic> _$ConfirmPaymentResponseToJson(
        ConfirmPaymentResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
    };

ApiResponse<T> _$ApiResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    ApiResponse<T>(
      success: json['success'] as bool,
      message: json['message'] as String?,
      data: _$nullableGenericFromJson(json['data'], fromJsonT),
      statusCode: (json['statusCode'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ApiResponseToJson<T>(
  ApiResponse<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': _$nullableGenericToJson(instance.data, toJsonT),
      'statusCode': instance.statusCode,
    };

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) =>
    input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) =>
    input == null ? null : toJson(input);

PlaceCheckRequest _$PlaceCheckRequestFromJson(Map<String, dynamic> json) =>
    PlaceCheckRequest(
      seller: json['seller'] as String,
    );

Map<String, dynamic> _$PlaceCheckRequestToJson(PlaceCheckRequest instance) =>
    <String, dynamic>{
      'seller': instance.seller,
    };

PlaceCheckResponse _$PlaceCheckResponseFromJson(Map<String, dynamic> json) =>
    PlaceCheckResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'],
    );

Map<String, dynamic> _$PlaceCheckResponseToJson(PlaceCheckResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };
