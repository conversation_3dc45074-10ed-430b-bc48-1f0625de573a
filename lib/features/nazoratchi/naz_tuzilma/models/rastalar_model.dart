/// Models for Rastalar (Grid/Seat Management) system
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'rastalar_model.g.dart';

/// Enum for square status (legacy compatibility)
enum SquareStatus {
  available, //  Orange - not occupied yet
  paid, // Green - occupied and paid
  unpaid, // Red - occupied but unpaid
  unbinded, // Gray - unbinded status
}

/// Tariff model from API response
@JsonSerializable()
class ApiTariff extends Equatable {
  @JsonKey(name: '_id')
  final String id;
  final String title;
  final int price;

  const ApiTariff({
    required this.id,
    required this.title,
    required this.price,
  });

  factory ApiTariff.fromJson(Map<String, dynamic> json) =>
      _$ApiTariffFromJson(json);
  Map<String, dynamic> toJson() => _$ApiTariffToJson(this);

  @override
  List<Object?> get props => [id, title, price];
}

/// API Seller model
@JsonSerializable()
class ApiSeller extends Equatable {
  @JsonKey(name: '_id')
  final String id;
  final String firstName;
  final String lastName;
  final String? middleName;

  const ApiSeller({
    required this.id,
    required this.firstName,
    required this.lastName,
    this.middleName,
  });

  factory ApiSeller.fromJson(Map<String, dynamic> json) =>
      _$ApiSellerFromJson(json);
  Map<String, dynamic> toJson() => _$ApiSellerToJson(this);

  /// Get full name
  String get fullName {
    final parts = [firstName, middleName, lastName]
        .where((part) => part != null && part.isNotEmpty);
    return parts.join(' ');
  }

  @override
  List<Object?> get props => [id, firstName, lastName, middleName];
}

/// API Square model from response
/// Status codes: 1=belgilanmagan, 2=bo'sh, 3=qarzdor, 4=to'langan
@JsonSerializable()
class ApiSquare extends Equatable {
  @JsonKey(name: '_id')
  final String id;
  final String place;
  final ApiTariff tariff;
  final int squareMeter;
  final int price;

  /// Status codes: 1=belgilanmagan, 2=bo'sh, 3=qarzdor, 4=to'langan
  final int status;
  final int title;
  @JsonKey(name: 'payment', defaultValue: false)
  final bool payment;
  final String? supervisor;
  final ApiSeller? seller;
  final String date;
  final String? photo;
  final String? desc;

  const ApiSquare({
    required this.id,
    required this.place,
    required this.tariff,
    required this.squareMeter,
    required this.price,
    required this.status,
    required this.title,
    required this.payment,
    this.supervisor,
    this.seller,
    required this.date,
    this.photo,
    this.desc,
  });

  factory ApiSquare.fromJson(Map<String, dynamic> json) =>
      _$ApiSquareFromJson(json);
  Map<String, dynamic> toJson() => _$ApiSquareToJson(this);

  @override
  List<Object?> get props => [
        id,
        place,
        tariff,
        squareMeter,
        price,
        status,
        title,
        payment,
        supervisor,
        seller,
        date,
        photo,
        desc
      ];
}

/// Grouped squares by seller for multi-occupant display
class GroupedSquares extends Equatable {
  final String? sellerId;
  final List<ApiSquare> squares;
  final int totalPrice;
  final bool isPaid;
  final String tariffTitle;

  const GroupedSquares({
    this.sellerId,
    required this.squares,
    required this.totalPrice,
    required this.isPaid,
    required this.tariffTitle,
  });

  /// Get the primary status based on API status codes
  /// 1 = belgilanmagan (Unbinded), 2 = bo'sh (Empty), 3 = qarzdor (Unpaid), 4 = to'langan (Paid)
  SquareStatus get primaryStatus {
    if (squares.isEmpty) return SquareStatus.available;

    final firstSquare = squares.first;
    switch (firstSquare.status) {
      case 1:
        return SquareStatus.unbinded; // belgilanmagan
      case 2:
        return SquareStatus.available; // bo'sh
      case 3:
        return SquareStatus.unpaid; // qarzdor
      case 4:
        return SquareStatus.paid; // to'langan
      default:
        return SquareStatus.available;
    }
  }

  /// Get display title for the group
  String get displayTitle {
    if (squares.length == 1) {
      return 'Rasta #${squares.first.title}';
    }
    final numbers = squares.map((s) => s.title).toList()..sort();
    return 'Rasta #${numbers.join(', ')}';
  }

  /// Get square numbers for display
  List<int> get squareNumbers {
    return squares.map((s) => s.title).toList()..sort();
  }

  /// Check if this is a multi-occupant group
  bool get isMultiOccupant => squares.length > 1;

  /// Check if this square has sub-occupants (for compatibility)
  bool get hasSubOccupants => squares.length > 1;

  /// Get seller name (owner name)
  String get sellerName {
    if (squares.isNotEmpty && squares.first.seller != null) {
      return squares.first.seller!.fullName;
    }
    return 'Noma\'lum sotuvchi'; // Default if no seller
  }

  @override
  List<Object?> get props =>
      [sellerId, squares, totalPrice, isPaid, tariffTitle];
}

/// API response wrapper
@JsonSerializable()
class ApiSquareResponse extends Equatable {
  final List<ApiSquare> data;
  final bool success;
  final String? message;

  const ApiSquareResponse({
    required this.data,
    this.success = true,
    this.message,
  });

  factory ApiSquareResponse.fromJson(Map<String, dynamic> json) =>
      _$ApiSquareResponseFromJson(json);
  Map<String, dynamic> toJson() => _$ApiSquareResponseToJson(this);

  @override
  List<Object?> get props => [data, success, message];
}

/// Model for individual square/seat
class RastalarSquare {
  final int number;
  final SquareStatus status;
  final List<int>? subNumbers; // For multi-occupant squares
  final ApiSeller? seller; // Add seller information

  const RastalarSquare({
    required this.number,
    required this.status,
    this.subNumbers,
    this.seller,
  });

  /// Check if this square is occupied (paid, unpaid, or unbinded)
  bool get isOccupied =>
      status == SquareStatus.paid ||
      status == SquareStatus.unpaid ||
      status == SquareStatus.unbinded;

  /// Check if this square is a main square (contains sub-squares)
  /// This is determined by the presence of subNumbers
  bool get isMainSquare => subNumbers != null && subNumbers!.isNotEmpty;

  /// Check if this square has sub-occupants
  bool get hasSubOccupants => subNumbers != null && subNumbers!.isNotEmpty;

  factory RastalarSquare.fromJson(Map<String, dynamic> json) {
    return RastalarSquare(
      number: json['number'] as int,
      status: SquareStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SquareStatus.available,
      ),
      subNumbers: json['sub_numbers'] != null
          ? List<int>.from(json['sub_numbers'])
          : null,
      seller:
          json['seller'] != null ? ApiSeller.fromJson(json['seller']) : null,
    );
  }

  /// Convert from GroupedSquares to RastalarSquare (for compatibility)
  factory RastalarSquare.fromGroupedSquares(GroupedSquares grouped) {
    // Use the primary status from API mapping
    final status = grouped.primaryStatus;

    return RastalarSquare(
      number: grouped.squares.first.title,
      status: status,
      subNumbers: grouped.isMultiOccupant ? grouped.squareNumbers : null,
      seller: grouped.squares.first.seller, // Include seller information
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'status': status.name,
      'sub_numbers': subNumbers,
      'seller': seller?.toJson(),
    };
  }
}

/// Legacy model for the entire page grid/block (for backward compatibility)
class RastalarBlock {
  final String blockId;
  final String blockName;
  final List<RastalarSquare> squares;
  final DateTime? lastUpdated;

  const RastalarBlock({
    required this.blockId,
    required this.blockName,
    required this.squares,
    this.lastUpdated,
  });

  /// Get all squares that should be displayed in the main grid
  /// (excludes sub-numbers that are part of multi-occupant squares)
  List<RastalarSquare> get displaySquares {
    Set<int> subNumbers = {};

    // Collect all sub-numbers
    for (var square in squares) {
      if (square.hasSubOccupants) {
        subNumbers
            .addAll(square.subNumbers!.skip(1)); // Skip main square number
      }
    }

    // Return squares that are not sub-numbers
    return squares
        .where((square) => !subNumbers.contains(square.number))
        .toList();
  }

  /// Get squares by status
  List<RastalarSquare> getSquaresByStatus(SquareStatus status) {
    return squares.where((square) => square.status == status).toList();
  }

  /// Get total squares count
  int get totalSquares => squares.length;

  /// Get occupied squares count
  int get occupiedCount => squares.where((s) => s.isOccupied).length;

  factory RastalarBlock.fromJson(Map<String, dynamic> json) {
    return RastalarBlock(
      blockId: json['block_id'] as String,
      blockName: json['block_name'] as String,
      squares: (json['squares'] as List)
          .map((square) => RastalarSquare.fromJson(square))
          .toList(),
      lastUpdated: json['last_updated'] != null
          ? DateTime.parse(json['last_updated'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'block_id': blockId,
      'block_name': blockName,
      'squares': squares.map((square) => square.toJson()).toList(),
      'total_squares': totalSquares,
      'occupied_count': occupiedCount,
      'last_updated': lastUpdated?.toIso8601String(),
    };
  }
}
