import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../models/rastalar_model.dart';
import '../bloc/rastalar_api/rastalar_api_bloc.dart';

/// Page widget for reporting empty squares with image upload
class EmptySquareReportPage extends StatefulWidget {
  final RastalarSquare square;
  final GroupedSquares? groupedSquares; // For API-based squares
  final String? pavilionId;
  final VoidCallback? onSuccess;

  const EmptySquareReportPage({
    super.key,
    required this.square,
    this.groupedSquares,
    this.pavilionId,
    this.onSuccess,
  });

  /// Constructor for API-based grouped squares
  const EmptySquareReportPage.fromGroupedSquares({
    super.key,
    required GroupedSquares groupedSquares,
    this.pavilionId,
    this.onSuccess,
  })  : square = const RastalarSquare(
            number: 0, status: SquareStatus.available, seller: null),
        groupedSquares = groupedSquares;

  @override
  State<EmptySquareReportPage> createState() => _EmptySquareReportPageState();
}

class _EmptySquareReportPageState extends State<EmptySquareReportPage> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<RastalarApiBloc>(),
      child: _EmptySquareReportContent(
        square: widget.square,
        groupedSquares: widget.groupedSquares,
        pavilionId: widget.pavilionId,
        onSuccess: widget.onSuccess,
      ),
    );
  }
}

class _EmptySquareReportContent extends StatefulWidget {
  final RastalarSquare square;
  final GroupedSquares? groupedSquares;
  final String? pavilionId;
  final VoidCallback? onSuccess;

  const _EmptySquareReportContent({
    required this.square,
    this.groupedSquares,
    this.pavilionId,
    this.onSuccess,
  });

  @override
  State<_EmptySquareReportContent> createState() =>
      _EmptySquareReportContentState();
}

class _EmptySquareReportContentState extends State<_EmptySquareReportContent> {
  File? _selectedImage;
  final ImagePicker _picker = ImagePicker();
  final TextEditingController _descriptionController = TextEditingController();
  bool _isUploading = false;
  String? _selectedSubSquare; // For multi-occupant squares - now uses square ID
  Timer? _delayedTimer;

  // Quick tag options
  final List<String> _quickTags = [
    'Bo\'sh rasta',
    'Yaroqsiz rasta',
    'Ta\'mirlash kerak',
    'Buzilgan jihozlar',
  ];

  @override
  void dispose() {
    _descriptionController.dispose();
    cancelDelayedOperation();
    super.dispose();
  }

  /// Check if we should show the square selector
  bool _shouldShowSquareSelector() {
    // If we have grouped squares (API mode), check if it's multi-occupant
    if (widget.groupedSquares != null) {
      return widget.groupedSquares!.isMultiOccupant;
    }
    // Otherwise, use the legacy square's hasSubOccupants
    return widget.square.hasSubOccupants;
  }

  // Method to cancel the delayed operation
  void cancelDelayedOperation() {
    if (_delayedTimer != null && _delayedTimer!.isActive) {
      _delayedTimer!.cancel();
      _delayedTimer = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RastalarApiBloc, RastalarApiState>(
      listener: (context, state) {
        if (state.status == RastalarApiStatus.success) {
          setState(() {
            _isUploading = false;
          });
          _showSuccessDialog();
          _delayedTimer = Timer(const Duration(seconds: 3), () {
            if (mounted) {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Close page

              // Call the success callback to refresh the grid
              if (widget.onSuccess != null) {
                widget.onSuccess!();
              }
            }
          });
        } else if (state.status == RastalarApiStatus.failure) {
          setState(() {
            _isUploading = false;
          });
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message ?? 'Xatolik yuz berdi'),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
        }
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          elevation: 0,
          leading: IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.arrow_back,
              color: Theme.of(context).colorScheme.onSurface,
              size: 24,
            ),
          ),
          title: Text(
            'Bo\'sh rasta deb belgilash',
            style: AppTextStyles.titleMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: Stack(
          children: [
            SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Square selection section (for multi-occupant squares)
                  if (_shouldShowSquareSelector()) ...[
                    _buildSquareSelectionSection(),
                    const SizedBox(height: 24),
                  ],

                  // Image upload section
                  Text(
                    'Rasm yuklash',
                    style: AppTextStyles.titleMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Bo\'sh joy rasmini yuklang',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Image upload area
                  _buildImageUploadArea(),

                  const SizedBox(height: 24),

                  // Description section
                  Text(
                    'Izoh yozish',
                    style: AppTextStyles.titleMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Quick tags
                  _buildQuickTags(),

                  const SizedBox(height: 16),

                  // Description text field
                  Container(
                    height: 120,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextField(
                      controller: _descriptionController,
                      maxLines: null,
                      expands: true,
                      textAlignVertical: TextAlignVertical.top,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      decoration: InputDecoration(
                        hintText: _selectedImage != null
                            ? 'Ushbu rasta hozirda bo\'sh. Savdo faoliyati olib borilmayapti. Ijaraga berish uchun ochiq'
                            : '',
                        hintStyle: AppTextStyles.bodyMedium.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.all(16),
                      ),
                      onChanged: (value) {
                        setState(() {});
                      },
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Submit button
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: _canSubmit() ? _handleSubmit : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _canSubmit()
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isUploading
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Theme.of(context).colorScheme.onPrimary,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              'Yuborish',
                              style: AppTextStyles.bodyLarge.copyWith(
                                color: Theme.of(context).colorScheme.onPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),

                  const SizedBox(height: 100), // Extra space for bottom
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageUploadArea() {
    if (_selectedImage != null) {
      return Stack(
        children: [
          Container(
            width: double.infinity,
            height: 160,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.file(
                _selectedImage!,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedImage = null;
                });
              },
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.error,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  color: Theme.of(context).colorScheme.onError,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      );
    }

    return GestureDetector(
      onTap: () => _pickImage(ImageSource.camera),
      child: DottedBorder(
        color: Theme.of(context).colorScheme.primary,
        strokeWidth: 2,
        dashPattern: const [8, 4],
        borderType: BorderType.RRect,
        radius: const Radius.circular(12),
        child: Container(
          width: double.infinity,
          height: 160,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: SvgPicture.asset(
                    'assets/icons/gallery_icon.svg',
                    colorFilter: ColorFilter.mode(
                      Theme.of(context).colorScheme.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'Rasm yuklash',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickTags() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _quickTags.map((tag) {
        return GestureDetector(
          onTap: () {
            setState(() {
              _descriptionController.text = tag;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary,
                width: 1,
              ),
            ),
            child: Text(
              tag,
              style: AppTextStyles.bodySmall.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  bool _canSubmit() {
    // For multi-occupant squares, a sub-square must be selected
    if (_shouldShowSquareSelector() && _selectedSubSquare == null) {
      return false;
    }

    return _selectedImage != null &&
        _descriptionController.text.trim().isNotEmpty &&
        !_isUploading;
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Rasm tanlash',
              style: AppTextStyles.titleMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildImagePickerOption(
                  icon: Icons.camera_alt,
                  label: 'Kamera',
                  onTap: () => _pickImage(ImageSource.camera),
                ),
                _buildImagePickerOption(
                  icon: Icons.photo_library,
                  label: 'Galereya',
                  onTap: () => _pickImage(ImageSource.gallery),
                ),
              ],
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildImagePickerOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
          source: source,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 85,
          preferredCameraDevice: CameraDevice.rear);

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Rasm tanlashda xatolik yuz berdi'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _handleSubmit() async {
    print('🚀 [UI] _handleSubmit called');
    if (!_canSubmit()) {
      print('❌ [UI] Cannot submit - validation failed');
      return;
    }

    setState(() {
      _isUploading = true;
    });
    print('🔄 [UI] Set uploading state to true');

    // Debug widget data
    print('🔍 [UI] Widget data:');
    print('   - Square number: ${widget.square.number}');
    print('   - GroupedSquares is null: ${widget.groupedSquares == null}');
    if (widget.groupedSquares != null) {
      print(
          '   - GroupedSquares squares count: ${widget.groupedSquares!.squares.length}');
      if (widget.groupedSquares!.squares.isNotEmpty) {
        print(
            '   - First square place: ${widget.groupedSquares!.squares.first.place}');
        print(
            '   - First square data: ${widget.groupedSquares!.squares.first}');
      }
    }

    // Get place ID from grouped squares or fallback to square number
    String placeId;
    if (widget.groupedSquares != null &&
        widget.groupedSquares!.squares.isNotEmpty) {
      // Use the selected sub-square's place ID if a specific square is selected
      if (_selectedSubSquare != null) {
        final selectedSquare = widget.groupedSquares!.squares.firstWhere(
          (square) => square.id == _selectedSubSquare,
          orElse: () => widget.groupedSquares!.squares.first,
        );
        placeId = selectedSquare.place;
        print(
            '✅ [UI] Using place ID from selected sub-square: $placeId (square ID: ${selectedSquare.id})');
      } else {
        placeId = widget.groupedSquares!.squares.first.place;
        print('✅ [UI] Using place ID from first GroupedSquare: $placeId');
      }
    } else {
      // Fallback for legacy usage - this shouldn't happen in normal flow
      placeId = widget.square.number.toString();
      print('⚠️ [UI] Using fallback place ID from square number: $placeId');
    }

    final description = _descriptionController.text.trim();
    final imagePath = _selectedImage!.path;

    print('📝 [UI] Form data:');
    print('   - Place ID: $placeId');
    print('   - Description: $description');
    print('   - Image path: $imagePath');
    print('   - Image exists: ${File(imagePath).existsSync()}');

    // Submit free place report via bloc
    print('📤 [UI] Dispatching SubmitFreePlaceReport event to bloc');
    context.read<RastalarApiBloc>().add(
          SubmitFreePlaceReport(
            placeId: placeId,
            description: description,
            imagePath: imagePath,
          ),
        );
    print('✅ [UI] Event dispatched successfully');
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 3,
                  ),
                ),
                child: Icon(
                  Icons.check,
                  color: Theme.of(context).colorScheme.primary,
                  size: 40,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Hisobot muvaffaqiyatli yuborildi',
                textAlign: TextAlign.center,
                style: AppTextStyles.titleMedium.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Sizning hisobotingiz muvaffaqiyatli yuborildi va ko\'rib chiqiladi',
                textAlign: TextAlign.center,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    cancelDelayedOperation();
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();// Close dialog
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Yaxshi',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build square selection section for multi-occupant squares
  Widget _buildSquareSelectionSection() {
    if (!_shouldShowSquareSelector()) return const SizedBox.shrink();

    // Get squares from the appropriate source
    List<ApiSquare> squares;
    if (widget.groupedSquares != null) {
      // API mode: use grouped squares
      squares = widget.groupedSquares!.squares;
    } else {
      // Legacy mode: create dummy squares from sub numbers
      squares = widget.square.subNumbers!
          .map((number) => ApiSquare(
                id: number.toString(),
                place: number.toString(),
                tariff: const ApiTariff(
                  id: 'dummy',
                  title: 'Legacy',
                  price: 0,
                ),
                squareMeter: 0,
                price: 0,
                status: 0,
                title: number,
                payment: false,
                date: '',
                photo: null,
                desc: null,
              ))
          .toList();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Qaysi rastani bo\'shatmoqchisiz',
          style: AppTextStyles.titleMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),

        // Square selection buttons
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: squares.map((square) {
            final isSelected = _selectedSubSquare == square.id;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedSubSquare = square.id;
                });
              },
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.outline,
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Text(
                    '#${square.title}',
                    style: AppTextStyles.titleMedium.copyWith(
                      color: isSelected
                          ? Theme.of(context).colorScheme.onPrimary
                          : Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
