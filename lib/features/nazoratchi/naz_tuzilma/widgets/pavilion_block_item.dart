import 'package:click_bazaar/core/function/functions.dart';
import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:click_bazaar/core/theme/app_text_styles.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import '../models/pavilion_model.dart';
import 'status_item.dart';

class PavilionBlockItem extends StatelessWidget {
  final BlockData blockData;
  final VoidCallback onClick;

  const PavilionBlockItem({
    super.key,
    required this.blockData,
    required this.onClick,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onClick,
      child: Container(
        margin: EdgeInsets.only(bottom: 16.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(cRadius12.r),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            Gap(16.h),
            _buildStatusItems(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              blockData.title,
              style: AppTextStyles.titleMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
                fontSize: 18.sp,
              ),
            ),
            Text(
              "Batafsil",
              style: AppTextStyles.bodySmall.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
                fontSize: 12.sp,
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Rastalar",
              style: AppTextStyles.titleMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
                fontSize: 14.sp,
              ),
            ),
            Text(
              rastalarCount(blockData).toString(),
              style: AppTextStyles.bodySmall.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
                fontSize: 24.sp,
              ),
            ),
          ],
        ),
      ],
    );
  }


  Widget _buildStatusItems(BuildContext context) {
    final availableStatuses = blockData.availableStatuses;

    if (availableStatuses.isEmpty) {
      return Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(cRadius8.r),
        ),
        child: Center(
          child: Text(
            'Ma\'lumot mavjud emas',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      );
    }

    return Column(
      children: availableStatuses.map((status) {
        return Padding(
          padding: EdgeInsets.only(bottom: 8.h),
          child: _buildStatusItem(status),
        );
      }).toList(),
    );
  }

  Widget _buildStatusItem(BlockStatus status) {
    final statusName = blockData.getStatusName(status.status);
    final color = _getStatusColor(status.status);
    final icon = _getStatusIcon(status.status);

    return NazStatusItem(
      title: statusName,
      count: status.count.toString(),
      price: status.totalPrice > 0
          ? '${_formatPrice(status.totalPrice)} UZS'
          : null,
      color: color,
      icon: icon,
      textColor: null, // Let the widget handle theme-aware colors
      status: status.status,
    );
  }

  Color _getStatusColor(int statusCode) {
    switch (statusCode) {
      case 1: // belgilanmagan
        return AppColors.cGrayDarkColor;
      case 2: // bo'sh
        return AppColors.cYellowishColor;
      case 3: // qarzdor
        return AppColors.cReddishColor;
      case 4: // to'langan
        return AppColors.cGreenishColor;
      default:
        return AppColors.cGrayDarkColor;
    }
  }

  String _getStatusIcon(int statusCode) {
    switch (statusCode) {
      case 1: // belgilanmagan
        return Assets.iconsBelgilanmagan;
      case 2: // bo'sh
        return Assets.iconsBosh;
      case 3: // qarzdor
        return Assets.iconsTolanmagan;
      case 4: // to'langan
        return Assets.iconsTolangan;
      default:
        return Assets.iconsBelgilanmagan;
    }
  }

  String _formatPrice(int price) {
    // Format price with thousands separator
    return price.toString().replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]} ',
        );
  }

}
