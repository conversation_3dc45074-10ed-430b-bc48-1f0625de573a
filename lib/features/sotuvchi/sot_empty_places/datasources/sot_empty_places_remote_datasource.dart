import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/empty_place_model.dart';

/// Abstract class for sot empty places remote data source
abstract class SotEmptyPlacesRemoteDatasource {
  Future<EmptyPlacesResponse> getFreePlaces({
    required String marketId,
    int page = 1,
    int limit = 20,
  });
}

/// Implementation of sot empty places remote data source
class SotEmptyPlacesRemoteDatasourceImpl implements SotEmptyPlacesRemoteDatasource {
  final Dio _dio;
  final NetworkInfo _networkInfo;

  SotEmptyPlacesRemoteDatasourceImpl({
    required Dio dio,
    required NetworkInfo networkInfo,
  })  : _dio = dio,
        _networkInfo = networkInfo;

  @override
  Future<EmptyPlacesResponse> getFreePlaces({
    required String marketId,
    int page = 1,
    int limit = 20,
  }) async {
    // Check network connectivity first
    if (!await _networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.freePlacesPath),
        type: DioExceptionType.connectionError,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
      );
    }

    try {
      debugPrint('SotEmptyPlaces: Fetching free places - page: $page, limit: $limit, marketId: $marketId');

      final response = await _dio.get(
        ApiPath.freePlacesPath,
        queryParameters: {
          'market': marketId,
          'page': page,
          'limit': limit,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      debugPrint('SotEmptyPlaces: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;
        debugPrint('SotEmptyPlaces: Response data type: ${data.runtimeType}');

        // Handle both direct array response and paginated response
        if (data is List) {
          // If API returns direct array, wrap it in pagination structure
          debugPrint('SotEmptyPlaces: ${data.length} places found (direct array)');
          return EmptyPlacesResponse(
            docs: data
                .map((e) => EmptyPlace.fromJson(e as Map<String, dynamic>))
                .toList(),
            totalDocs: data.length,
            limit: limit,
            totalPages: 1,
            page: page,
            pagingCounter: 1,
            hasPrevPage: false,
            hasNextPage: false,
            prevPage: null,
            nextPage: null,
          );
        } else if (data is Map<String, dynamic>) {
          // If API returns paginated response
          return EmptyPlacesResponse.fromJson(data);
        } else {
          throw DioException(
            requestOptions: RequestOptions(path: ApiPath.freePlacesPath),
            type: DioExceptionType.badResponse,
            message: 'Noto\'g\'ri javob formati',
          );
        }
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.freePlacesPath),
          type: DioExceptionType.badResponse,
          message: 'Server xatoligi: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      debugPrint('SotEmptyPlaces: DioException - ${e.message}');
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      debugPrint('SotEmptyPlaces: Unexpected error - $e');
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.freePlacesPath),
        type: DioExceptionType.unknown,
        error: e,
        message: 'Kutilmagan xatolik: $e',
      );
    }
  }
}
