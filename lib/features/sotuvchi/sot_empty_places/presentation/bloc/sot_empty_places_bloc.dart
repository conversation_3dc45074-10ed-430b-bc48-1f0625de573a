import 'package:bloc/bloc.dart';
import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:equatable/equatable.dart';
import '../../datasources/sot_empty_places_remote_datasource.dart';
import '../../models/empty_place_model.dart';

part 'sot_empty_places_event.dart';
part 'sot_empty_places_state.dart';

/// BLoC for managing sot empty places state
class SotEmptyPlacesBloc extends Bloc<SotEmptyPlacesEvent, SotEmptyPlacesState>
    with ErrorHandlerMixin<SotEmptyPlacesEvent, SotEmptyPlacesState> {
  final SotEmptyPlacesRemoteDatasource _remoteDatasource;
  final NetworkInfo _networkInfo;

  SotEmptyPlacesBloc({
    required SotEmptyPlacesRemoteDatasource remoteDatasource,
    required NetworkInfo networkInfo,
  })  : _remoteDatasource = remoteDatasource,
        _networkInfo = networkInfo,
        super(const SotEmptyPlacesState()) {
    on<LoadFreePlaces>(_onLoadFreePlaces);
    on<RefreshFreePlaces>(_onRefreshFreePlaces);
    on<LoadMoreFreePlaces>(_onLoadMoreFreePlaces);
  }

  /// Handle load free places event
  Future<void> _onLoadFreePlaces(
    LoadFreePlaces event,
    Emitter<SotEmptyPlacesState> emit,
  ) async {
    await executeApiCall<EmptyPlacesResponse>(
      apiCall: () => _remoteDatasource.getFreePlaces(
        marketId: event.marketId,
        page: event.page,
        limit: event.limit,
      ),
      onLoading: () {
        if (event.isRefresh) {
          emit(state.copyWith(
            isRefreshing: true,
            status: SotEmptyPlacesStatus.loading,
            errorType: null,
            hasRefreshError: false,
          ));
        } else {
          emit(state.copyWith(
            status: SotEmptyPlacesStatus.loading,
            errorType: null,
            hasRefreshError: false,
          ));
        }
      },
      onSuccess: (response) {
        // For initial load or refresh, replace the list
        // For pagination, this will be handled in _onLoadMoreFreePlaces
        final List<EmptyPlace> newEmptyPlaces;
        if (event.isRefresh || event.page == 1) {
          newEmptyPlaces = response.docs;
        } else {
          // This shouldn't happen in this handler, but keeping for safety
          newEmptyPlaces = [...state.emptyPlaces, ...response.docs];
        }

        emit(state.copyWith(
          status: SotEmptyPlacesStatus.success,
          emptyPlaces: newEmptyPlaces,
          currentPage: response.page,
          totalPages: response.totalPages,
          hasNextPage: response.hasNextPage,
          isRefreshing: false,
          isLoadingMore: false,
          message: null,
          errorType: null,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) => emit(state.copyWith(
        status: SotEmptyPlacesStatus.failure,
        message: message,
        errorType: _determineErrorType(message),
        isRefreshing: false,
        isLoadingMore: false,
        hasRefreshError: false,
      )),
    );
  }

  /// Handle refresh free places event
  Future<void> _onRefreshFreePlaces(
    RefreshFreePlaces event,
    Emitter<SotEmptyPlacesState> emit,
  ) async {
    // Set refreshing flag and keep existing data
    emit(state.copyWith(
      isRefreshing: true,
      errorType: null,
      hasRefreshError: false,
    ));

    // Check network connectivity first for refresh operations
    if (!await isNetworkConnected()) {
      // If no network during refresh, emit network error but keep existing data
      emit(state.copyWith(
        status: state.hasAnyEmptyPlaces ? SotEmptyPlacesStatus.success : SotEmptyPlacesStatus.failure,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
        errorType: SotEmptyPlacesErrorType.network,
        isRefreshing: false,
        hasRefreshError: true,
      ));
      return;
    }

    // Attempt to load fresh data
    await executeApiCall<EmptyPlacesResponse>(
      apiCall: () => _remoteDatasource.getFreePlaces(marketId: event.marketId, page: 1, limit: 20),
      onLoading: () {
        // Don't change status to loading during refresh to keep existing content visible
      },
      onSuccess: (response) {
        emit(state.copyWith(
          status: SotEmptyPlacesStatus.success,
          emptyPlaces: response.docs,
          currentPage: response.page,
          totalPages: response.totalPages,
          hasNextPage: response.hasNextPage,
          isRefreshing: false,
          isLoadingMore: false,
          message: null,
          errorType: null,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) {
        // Keep existing data and just set error info for toast
        emit(state.copyWith(
          status: state.hasAnyEmptyPlaces ? SotEmptyPlacesStatus.success : SotEmptyPlacesStatus.failure,
          message: message,
          errorType: _determineErrorType(message),
          isRefreshing: false,
          hasRefreshError: true,
        ));
      },
    );
  }

  /// Handle loading more free places (pagination)
  Future<void> _onLoadMoreFreePlaces(
    LoadMoreFreePlaces event,
    Emitter<SotEmptyPlacesState> emit,
  ) async {
    // Don't load more if already loading, no next page, or currently refreshing
    if (state.isLoadingMore || !state.hasNextPage || state.isRefreshing) {
      return;
    }

    final nextPage = state.currentPage + 1;

    await executeApiCall<EmptyPlacesResponse>(
      apiCall: () => _remoteDatasource.getFreePlaces(
        marketId: event.marketId,
        page: nextPage,
        limit: 20,
      ),
      onLoading: () {
        emit(state.copyWith(isLoadingMore: true));
      },
      onSuccess: (response) {
        // Append new items to existing list
        final updatedEmptyPlaces = [...state.emptyPlaces, ...response.docs];

        emit(state.copyWith(
          status: SotEmptyPlacesStatus.success,
          emptyPlaces: updatedEmptyPlaces,
          currentPage: response.page,
          totalPages: response.totalPages,
          hasNextPage: response.hasNextPage,
          isLoadingMore: false,
          message: null,
        ));
      },
      onFailure: (message) {
        emit(state.copyWith(
          isLoadingMore: false,
          message: message,
          errorType: _determineErrorType(message),
        ));
      },
    );
  }

  /// Determine error type based on error message
  SotEmptyPlacesErrorType _determineErrorType(String message) {
    if (message.contains('Internet aloqasi yo\'q') ||
        message.contains('ulanishni tekshiring') ||
        message.contains('connection') ||
        message.contains('network')) {
      return SotEmptyPlacesErrorType.network;
    }
    return SotEmptyPlacesErrorType.api;
  }

  /// Override network connectivity check
  @override
  Future<bool> isNetworkConnected() async {
    return await _networkInfo.isConnected;
  }
}
