import 'package:bloc/bloc.dart';
import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:equatable/equatable.dart';
import '../../datasources/payment_history_remote_datasource.dart';
import '../../models/payment_history_model.dart';

part 'payment_history_event.dart';
part 'payment_history_state.dart';

/// BLoC for managing payment history state
class PaymentHistoryBloc extends Bloc<PaymentHistoryEvent, PaymentHistoryState>
    with ErrorHandlerMixin<PaymentHistoryEvent, PaymentHistoryState> {
  final PaymentHistoryRemoteDatasource remoteDatasource;
  final NetworkInfo networkInfo;

  PaymentHistoryBloc({
    required this.remoteDatasource,
    required this.networkInfo,
  }) : super(const PaymentHistoryState()) {
    on<LoadPaymentHistoryEvent>(_handleLoadPaymentHistory);
    on<RefreshPaymentHistoryEvent>(_handleRefreshPaymentHistory);
    on<LoadMorePaymentHistoryEvent>(_handleLoadMorePaymentHistory);
  }

  /// Handle loading payment history
  Future<void> _handleLoadPaymentHistory(
    LoadPaymentHistoryEvent event,
    Emitter<PaymentHistoryState> emit,
  ) async {
    await executeApiCall<PaymentHistoryResponse>(
      apiCall: () => remoteDatasource.getPaymentHistory(
        page: event.page,
        limit: event.limit,
      ),
      onLoading: () {
        if (event.isRefresh) {
          emit(state.copyWith(
            isRefreshing: true,
            status: PaymentHistoryStatus.loading,
            errorType: null,
            hasRefreshError: false,
          ));
        } else {
          emit(state.copyWith(
            status: PaymentHistoryStatus.loading,
            errorType: null,
            hasRefreshError: false,
          ));
        }
      },
      onSuccess: (response) {
        // For initial load or refresh, replace the list
        // For pagination, this will be handled in _handleLoadMorePaymentHistory
        final List<PaymentHistory> newPaymentHistory;
        if (event.isRefresh || event.page == 1) {
          newPaymentHistory = response.docs;
        } else {
          // This shouldn't happen in this handler, but keeping for safety
          newPaymentHistory = [...state.paymentHistory, ...response.docs];
        }

        emit(state.copyWith(
          status: PaymentHistoryStatus.success,
          paymentHistory: newPaymentHistory,
          currentPage: response.page,
          totalPages: response.totalPages,
          hasNextPage: response.hasNextPage,
          isRefreshing: false,
          isLoadingMore: false,
          message: null,
          errorType: null,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) {
        emit(state.copyWith(
          status: PaymentHistoryStatus.failure,
          message: message,
          errorType: _determineErrorType(message),
          isRefreshing: false,
          isLoadingMore: false,
          hasRefreshError: false,
        ));
      },
    );
  }

  /// Handle refreshing payment history
  Future<void> _handleRefreshPaymentHistory(
    RefreshPaymentHistoryEvent event,
    Emitter<PaymentHistoryState> emit,
  ) async {
    // Set refreshing flag and keep existing data
    emit(state.copyWith(
      isRefreshing: true,
      errorType: null,
      hasRefreshError: false,
    ));

    // Check network connectivity first for refresh operations
    if (!await isNetworkConnected()) {
      // If no network during refresh, emit network error but keep existing data
      emit(state.copyWith(
        status: state.hasAnyPaymentHistory ? PaymentHistoryStatus.success : PaymentHistoryStatus.failure,
        message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
        errorType: PaymentHistoryErrorType.network,
        isRefreshing: false,
        hasRefreshError: true,
      ));
      return;
    }

    // Attempt to load fresh data
    await executeApiCall<PaymentHistoryResponse>(
      apiCall: () => remoteDatasource.getPaymentHistory(page: 1, limit: 20),
      onLoading: () {
        // Don't change status to loading during refresh to keep existing content visible
      },
      onSuccess: (response) {
        emit(state.copyWith(
          status: PaymentHistoryStatus.success,
          paymentHistory: response.docs,
          currentPage: response.page,
          totalPages: response.totalPages,
          hasNextPage: response.hasNextPage,
          isRefreshing: false,
          isLoadingMore: false,
          message: null,
          errorType: null,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) {
        // Keep existing data and just set error info for toast
        emit(state.copyWith(
          status: state.hasAnyPaymentHistory ? PaymentHistoryStatus.success : PaymentHistoryStatus.failure,
          message: message,
          errorType: _determineErrorType(message),
          isRefreshing: false,
          hasRefreshError: true,
        ));
      },
    );
  }

  /// Handle loading more payment history (pagination)
  Future<void> _handleLoadMorePaymentHistory(
    LoadMorePaymentHistoryEvent event,
    Emitter<PaymentHistoryState> emit,
  ) async {
    // Don't load more if already loading, no next page, or currently refreshing
    if (state.isLoadingMore || !state.hasNextPage || state.isRefreshing) {
      return;
    }

    final nextPage = state.currentPage + 1;

    await executeApiCall<PaymentHistoryResponse>(
      apiCall: () => remoteDatasource.getPaymentHistory(
        page: nextPage,
        limit: 20,
      ),
      onLoading: () {
        emit(state.copyWith(isLoadingMore: true));
      },
      onSuccess: (response) {
        // Append new items to existing list
        final updatedPaymentHistory = [...state.paymentHistory, ...response.docs];

        emit(state.copyWith(
          status: PaymentHistoryStatus.success,
          paymentHistory: updatedPaymentHistory,
          currentPage: response.page,
          totalPages: response.totalPages,
          hasNextPage: response.hasNextPage,
          isLoadingMore: false,
          message: null,
        ));
      },
      onFailure: (message) {
        emit(state.copyWith(
          isLoadingMore: false,
          message: message,
          errorType: _determineErrorType(message),
        ));
      },
    );
  }

  /// Override network connectivity check
  @override
  Future<bool> isNetworkConnected() async {
    return await networkInfo.isConnected;
  }

  /// Determine error type based on error message
  PaymentHistoryErrorType _determineErrorType(String message) {
    if (message.contains('Internet aloqasi yo\'q') ||
        message.contains('ulanishni tekshiring') ||
        message.contains('connection') ||
        message.contains('network')) {
      return PaymentHistoryErrorType.network;
    }
    return PaymentHistoryErrorType.api;
  }
}
