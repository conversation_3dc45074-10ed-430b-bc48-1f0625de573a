import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../core/widgets/universal_loading.dart';
import '../../../../../di/dependency_injection.dart';
import '../bloc/payment_history_bloc.dart';
import '../widget/payment_history_card.dart';
import '../widgets/payment_history_shimmer.dart';
import '../widgets/bottom_loading_widget.dart';
import '../../models/payment_history_model.dart';

class SotPaymentHistoryPage extends StatelessWidget {
  const SotPaymentHistoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di<PaymentHistoryBloc>()
        ..add(const LoadPaymentHistoryEvent()),
      child: const _SotPaymentHistoryView(),
    );
  }
}

class _SotPaymentHistoryView extends StatefulWidget {
  const _SotPaymentHistoryView();

  @override
  State<_SotPaymentHistoryView> createState() => _SotPaymentHistoryViewState();
}

class _SotPaymentHistoryViewState extends State<_SotPaymentHistoryView> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle scroll events for pagination
  void _onScroll() {
    if (_isBottom) {
      context.read<PaymentHistoryBloc>().add(const LoadMorePaymentHistoryEvent());
    }
  }

  /// Check if user has scrolled to bottom
  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9); // Trigger at 90% scroll
  }

  /// Handle refresh
  Future<void> _onRefresh() async {
    // Provide haptic feedback for better user experience
    HapticFeedback.lightImpact();

    // Add a small delay to show the refresh indicator
    await Future.delayed(const Duration(milliseconds: 300));
    context.read<PaymentHistoryBloc>().add(const RefreshPaymentHistoryEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        title: Text(
          'To\'lov tarixi',
          style: AppTextStyles.titleLarge.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: BlocConsumer<PaymentHistoryBloc, PaymentHistoryState>(
        listener: (context, state) {
          // Handle refresh errors with toast messages
          // Show toast when there's a refresh error and existing data
          if (state.hasRefreshError && state.hasAnyPaymentHistory && state.message != null) {
            // This is a refresh error - show toast while keeping existing content
            final message = state.isNetworkError
                ? 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.'
                : state.message!;

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message),
                backgroundColor: AppColors.cReddishColor,
                duration: const Duration(seconds: 3),
                action: state.isNetworkError ? SnackBarAction(
                  label: 'Qayta urinish',
                  textColor: AppColors.white,
                  onPressed: () {
                    context.read<PaymentHistoryBloc>().add(
                      const RefreshPaymentHistoryEvent(),
                    );
                  },
                ) : null,
              ),
            );
          }
        },
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: _onRefresh,
            color: AppColors.cFirstColor,
            backgroundColor: AppColors.cCardsColor,
            child: _buildContent(state),
          );
        },
      ),
    );
  }

  /// Build content based on state
  Widget _buildContent(PaymentHistoryState state) {
    // Show shimmer during initial loading or when refreshing (always show shimmer during refresh)
    if (state.isLoading || state.isInitial || state.isRefreshing) {
      return const PaymentHistoryShimmer();
    }

    // Handle initial load errors - show centered error widget only if no existing data
    if (state.isFailure && !state.hasAnyPaymentHistory) {
      return _buildErrorState(state);
    }

    if (state.isSuccess || state.hasAnyPaymentHistory) {
      return _buildPaymentHistoryList(state);
    }

    return const SizedBox.shrink();
  }

  /// Build payment history list
  Widget _buildPaymentHistoryList(PaymentHistoryState state) {
    if (state.paymentHistory.isEmpty) {
      return _buildEmptyStateScrollable();
    }

    return ListView.separated(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: state.paymentHistory.length + (state.isLoadingMore ? 1 : 0),
      physics: const AlwaysScrollableScrollPhysics(),
      separatorBuilder: (context, index) {
        // Don't add separator after the last item if loading more
        if (index == state.paymentHistory.length - 1 && state.isLoadingMore) {
          return const SizedBox.shrink();
        }
        return const SizedBox(height: 12);
      },
      itemBuilder: (context, index) {
        // Show loading widget at the bottom
        if (index == state.paymentHistory.length) {
          return const BottomLoadingWidget();
        }

        final payment = state.paymentHistory[index];
        return PaymentHistoryCard(
          paymentHistory: payment,
        );
      },
    );
  }

  /// Build empty state (scrollable for RefreshIndicator)
  Widget _buildEmptyStateScrollable() {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.8,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.payment_outlined,
                  size: 64,
                  color: AppColors.cTextGrayColor,
                ),
                SizedBox(height: 16),
                Text(
                  'To\'lov tarixi mavjud emas',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.cTextGrayColor,
                    fontSize: 16,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Hozircha hech qanday to\'lov amalga oshirilmagan',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.cTextGrayColor,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.payment_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: 16),
          Text(
            'To\'lov tarixi mavjud emas',
            style: AppTextStyles.bodyLarge.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Hozircha hech qanday to\'lov amalga oshirilmagan',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(PaymentHistoryState state) {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        Container(
          height: MediaQuery.of(context).size.height * 0.8,
          child: Center(
            child: UniversalLoading.error(
              message: state.isNetworkError
                  ? 'Internet aloqasi yo\'q.\nIltimos, ulanishni tekshiring.'
                  : (state.message ?? 'Ma\'lumotlarni yuklashda xatolik yuz berdi'),
              onRetry: () {
                context.read<PaymentHistoryBloc>().add(const LoadPaymentHistoryEvent());
              },
              retryButtonText: 'Qayta urinish',
              icon: state.isNetworkError ? Icons.wifi_off : null,
            ),
          ),
        ),
      ],
    );
  }
}
