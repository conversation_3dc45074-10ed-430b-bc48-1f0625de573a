import 'package:click_bazaar/core/function/functions.dart';
import 'package:dio/dio.dart';
import '../../../../../core/utils/api_path.dart';
import '../../../../../core/network/network_info.dart';
import '../../models/sotuvchi_profile_model.dart';

/// Abstract class for sot profile remote data source
abstract class SotProfileRemoteDatasource {
  Future<SotuvchiProfileResponse> getUserProfile(String userId);

  Future<SotuvchiProfileResponse> updateProfileImage(
      String userId, String imagePath);
}

/// Implementation of sot profile remote data source
class SotProfileRemoteDatasourceImpl implements SotProfileRemoteDatasource {
  final Dio _dio;
  final NetworkInfo _networkInfo;

  SotProfileRemoteDatasourceImpl({
    required Dio dio,
    required NetworkInfo networkInfo,
  })  : _dio = dio,
        _networkInfo = networkInfo;

  @override
  Future<SotuvchiProfileResponse> getUserProfile(String userId) async {
    if (!await _networkInfo.isConnected) {
      return SotuvchiProfileResponse.error(
        'Internet aloqasi yo\'q. Ulanishni tekshiring.',
      );
    }

    try {
      final response = await _dio.get(
        '${ApiPath.sellerProfilePath}/$userId',
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        return SotuvchiProfileResponse.fromJson(response.data);
      } else {
        return SotuvchiProfileResponse.error(
          'Profil ma\'lumotlarini yuklashda xatolik yuz berdi',
        );
      }
    } on DioException {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (error) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.sellerProfilePath),
        type: DioExceptionType.unknown,
        error: error,
      );
    }
  }

  @override
  Future<SotuvchiProfileResponse> updateProfileImage(
      String userId, String imagePath) async {
    if (!await _networkInfo.isConnected) {
      return SotuvchiProfileResponse.error(
        'Internet aloqasi yo\'q. Ulanishni tekshiring.',
      );
    }

    try {
      // Create FormData for file upload
      final formData = FormData.fromMap({
        'file': [
          await MultipartFile.fromFile(imagePath,
              filename: imagePath.split('/').last,
              contentType: getMediaType(imagePath)),
        ],
      });
      print(imagePath);

      final response = await _dio.put(
        '${ApiPath.sellerUpdatePath}/$userId',
        data: formData,
        options: Options(
          headers: {
            "Content-Type": "multipart/form-data",
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        return SotuvchiProfileResponse.fromJson(response.data);
      } else {
        return SotuvchiProfileResponse.error(
          'Profil rasmini yangilashda xatolik yuz berdi',
        );
      }
    } on DioException {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (error) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.sellerUpdatePath),
        type: DioExceptionType.unknown,
        error: error,
      );
    }
  }
}
