  import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import 'package:click_bazaar/core/function/functions.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_storage/get_storage.dart';
import '../../models/sotuvchi_profile_model.dart';
import '../../data/datasources/sot_profile_remote_datasource.dart';
import 'sot_profile_event.dart';
import 'sot_profile_state.dart';

/// BLoC for managing sot profile state
class SotProfileBloc extends Bloc<SotProfileEvent, SotProfileState>
    with ErrorHandlerMixin<SotProfileEvent, SotProfileState> {
  final SotProfileRemoteDatasource _remoteDatasource;
  final GetStorage _storage;
  final NetworkInfo _networkInfo;

  SotProfileBloc({
    required SotProfileRemoteDatasource remoteDatasource,
    required GetStorage storage,
    required NetworkInfo networkInfo,
  })  : _remoteDatasource = remoteDatasource,
        _storage = storage,
        _networkInfo = networkInfo,
        super(const SotProfileInitial()) {
    on<LoadSotProfileEvent>(_onLoadSotProfile);
    on<RefreshSotProfileEvent>(_onRefreshSotProfile);
    on<ClearSotProfileEvent>(_onClearSotProfile);
    on<LoadCachedSotProfileEvent>(_onLoadCachedSotProfile);
    on<UpdateSotProfileImageEvent>(_onUpdateSotProfileImage);
  }

  /// Handle load sot profile event
  Future<void> _onLoadSotProfile(
    LoadSotProfileEvent event,
    Emitter<SotProfileState> emit,
  ) async {
    // Check if we should use cached data first
    if (!event.forceRefresh) {
      final cachedProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
        cacheKey: CacheKeys.sotuvchiProfile,
        fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
      );
      if (cachedProfile != null) {
        emit(SotProfileLoaded(
          userProfile: cachedProfile,
          isFromCache: true,
          message: 'Profile loaded from cache',
        ));
        return;
      }
    }

    emit(const SotProfileLoading());

    try {
      // Check network connectivity
      if (!await isNetworkConnected()) {
        final cachedProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
          cacheKey: CacheKeys.sotuvchiProfile,
          fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
        );
        emit(SotProfileError(
          message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
          cachedProfile: cachedProfile,
        ));
        return;
      }

      final response = await _remoteDatasource.getUserProfile(event.userId);

      if (response.success && response.sotuvchiProfile != null) {
        // Cache the profile
        await CacheHelper.cacheUserProfile<SotuvchiProfile>(
          cacheKey: CacheKeys.sotuvchiProfile,
          profile: response.sotuvchiProfile!,
          toJsonFunction: () => response.sotuvchiProfile!.toJson(),
        );

        // Extract and save market_id to GetStorage
        await _saveMarketId(response.sotuvchiProfile!);

        emit(SotProfileLoaded(
          userProfile: response.sotuvchiProfile!,
          isFromCache: false,
          message: response.message,
        ));
      } else {
        // Try to get cached profile as fallback
        final cachedProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
          cacheKey: CacheKeys.sotuvchiProfile,
          fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
        );
        emit(SotProfileError(
          message: response.error ?? 'Failed to load profile',
          cachedProfile: cachedProfile,
        ));
      }
    } catch (e) {
      print(e);
      // Handle errors using DioErrorHandler
      final errorMessage = SimpleErrorHandler.handleError(e);
      final cachedProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
        cacheKey: CacheKeys.sotuvchiProfile,
        fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
      );
      emit(SotProfileError(
        message: errorMessage,
        cachedProfile: cachedProfile,
      ));
    }
  }

  /// Handle refresh sot profile event
  Future<void> _onRefreshSotProfile(
    RefreshSotProfileEvent event,
    Emitter<SotProfileState> emit,
  ) async {
    // If we have current profile, show refreshing state
    if (state is SotProfileLoaded) {
      final currentState = state as SotProfileLoaded;
      emit(SotProfileRefreshing(currentProfile: currentState.userProfile));
    } else {
      emit(const SotProfileLoading());
    }

    try {
      // Check network connectivity
      if (!await isNetworkConnected()) {
        final cachedProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
          cacheKey: CacheKeys.sotuvchiProfile,
          fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
        );
        emit(SotProfileError(
          message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
          cachedProfile: cachedProfile,
        ));
        return;
      }

      final response = await _remoteDatasource.getUserProfile(event.userId);

      if (response.success && response.sotuvchiProfile != null) {
        // Cache the profile
        await CacheHelper.cacheUserProfile<SotuvchiProfile>(
          cacheKey: CacheKeys.sotuvchiProfile,
          profile: response.sotuvchiProfile!,
          toJsonFunction: () => response.sotuvchiProfile!.toJson(),
        );

        // Extract and save market_id to GetStorage
        await _saveMarketId(response.sotuvchiProfile!);

        emit(SotProfileLoaded(
          userProfile: response.sotuvchiProfile!,
          isFromCache: false,
          message: 'Profile refreshed successfully',
        ));
      } else {
        // Try to get cached profile as fallback
        final cachedProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
          cacheKey: CacheKeys.sotuvchiProfile,
          fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
        );
        emit(SotProfileError(
          message: response.error ?? 'Failed to refresh profile',
          cachedProfile: cachedProfile,
        ));
      }
    } catch (e) {
      // Handle errors using DioErrorHandler
      final errorMessage = SimpleErrorHandler.handleError(e);
      final cachedProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
        cacheKey: CacheKeys.sotuvchiProfile,
        fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
      );
      emit(SotProfileError(
        message: errorMessage,
        cachedProfile: cachedProfile,
      ));
    }
  }

  /// Handle clear sot profile event
  Future<void> _onClearSotProfile(
    ClearSotProfileEvent event,
    Emitter<SotProfileState> emit,
  ) async {
    try {
      await CacheHelper.clearCachedUserProfile(CacheKeys.sotuvchiProfile);
      emit(const SotProfileInitial());
    } catch (e) {
      // Even if clearing fails, reset to initial state
      emit(const SotProfileInitial());
    }
  }

  /// Handle load cached sot profile event
  Future<void> _onLoadCachedSotProfile(
    LoadCachedSotProfileEvent event,
    Emitter<SotProfileState> emit,
  ) async {
    try {
      final cachedProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
        cacheKey: CacheKeys.sotuvchiProfile,
        fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
      );
      if (cachedProfile != null) {
        emit(SotProfileLoaded(
          userProfile: cachedProfile,
          isFromCache: true,
          message: 'Profile loaded from cache',
        ));
      } else {
        emit(const SotProfileInitial());
      }
    } catch (e) {
      emit(const SotProfileInitial());
    }
  }

  /// Handle update sot profile image event
  Future<void> _onUpdateSotProfileImage(
    UpdateSotProfileImageEvent event,
    Emitter<SotProfileState> emit,
  ) async {
    // Get current profile for the updating state
    final currentState = state;
    SotuvchiProfile? currentProfile;

    if (currentState is SotProfileLoaded) {
      currentProfile = currentState.userProfile;
    } else {
      // Try to get cached profile
      currentProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
        cacheKey: CacheKeys.sotuvchiProfile,
        fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
      );
    }

    if (currentProfile != null) {
      emit(SotProfileImageUpdating(currentProfile: currentProfile));
    }

    try {
      // Check network connectivity
      if (!await isNetworkConnected()) {
        emit(SotProfileError(
          message: 'Internet aloqasi yo\'q. Iltimos, ulanishni tekshiring.',
          cachedProfile: currentProfile,
        ));
        return;
      }

      final response = await _remoteDatasource.updateProfileImage(
        event.userId,
        event.imagePath,
      );

      if (response.success && response.sotuvchiProfile != null) {
        // Cache the updated profile with new image URL
        await CacheHelper.cacheUserProfile<SotuvchiProfile>(
          cacheKey: CacheKeys.sotuvchiProfile,
          profile: response.sotuvchiProfile!,
          toJsonFunction: () => response.sotuvchiProfile!.toJson(),
        );

        emit(SotProfileImageUpdated(
          updatedProfile: response.sotuvchiProfile!,
          message: 'Profil rasmi muvaffaqiyatli yangilandi',
        ));

        // Then emit loaded state with updated profile (contains new image URL)
        emit(SotProfileLoaded(
          userProfile: response.sotuvchiProfile!,
          isFromCache: false,
          message: 'Profil rasmi yangilandi',
        ));
      } else {
        // On upload failure, reload the original profile from server to get the old image URL
        try {
          final originalProfileResponse = await _remoteDatasource.getUserProfile(event.userId);
          if (originalProfileResponse.success && originalProfileResponse.sotuvchiProfile != null) {
            await CacheHelper.cacheUserProfile<SotuvchiProfile>(
              cacheKey: CacheKeys.sotuvchiProfile,
              profile: originalProfileResponse.sotuvchiProfile!,
              toJsonFunction: () => originalProfileResponse.sotuvchiProfile!.toJson(),
            );
            emit(SotProfileImageUpdateFailed(
              message: response.error ?? 'Profil rasmini yangilashda xatolik yuz berdi',
              originalProfile: originalProfileResponse.sotuvchiProfile!,
            ));
            // Then emit loaded state with original profile
            emit(SotProfileLoaded(
              userProfile: originalProfileResponse.sotuvchiProfile!,
              isFromCache: false,
              message: 'Asl profil rasmi qaytarildi',
            ));
          } else {
            // If we can't reload, use cached profile
            final cachedProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
              cacheKey: CacheKeys.sotuvchiProfile,
              fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
            );
            emit(SotProfileError(
              message: response.error ?? 'Profil rasmini yangilashda xatolik yuz berdi',
              cachedProfile: cachedProfile,
            ));
          }
        } catch (reloadError) {
          // If reload fails, use cached profile
          final cachedProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
            cacheKey: CacheKeys.sotuvchiProfile,
            fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
          );
          emit(SotProfileError(
            message: 'Profil rasmini yangilashda xatolik yuz berdi',
            cachedProfile: cachedProfile,
          ));
        }
      }
    } catch (e) {
      // Handle errors using DioErrorHandler
      final errorMessage = SimpleErrorHandler.handleError(e);
      final cachedProfile = await CacheHelper.getCachedUserProfile<SotuvchiProfile>(
        cacheKey: CacheKeys.sotuvchiProfile,
        fromJsonFunction: (json) => SotuvchiProfile.fromJson(json),
      );
      emit(SotProfileError(
        message: errorMessage,
        cachedProfile: cachedProfile,
      ));
    }
  }

  /// Check if network is connected
  @override
  Future<bool> isNetworkConnected() async {
    return await _networkInfo.isConnected;
  }



  /// Extract and save market_id from profile to GetStorage
  Future<void> _saveMarketId(SotuvchiProfile profile) async {
    try {
      // Check if market object exists and has _id field
      if (profile.market != null && profile.market!.id.isNotEmpty) {
        await _storage.write(MARKET_ID, profile.market!.id);
        print('Market ID saved to storage: ${profile.market!.id}');
      } else {
        print('Market object is null or missing _id field');
        // Optionally remove any existing market_id if market is null
        await _storage.remove(MARKET_ID);
      }
    } catch (e) {
      print('Error saving market_id to storage: $e');
      // Don't throw error to avoid breaking the profile loading flow
    }
  }
}
