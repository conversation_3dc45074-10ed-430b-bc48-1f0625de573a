import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/page/naz_boz_tuzilma.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/presentation/pages/naz_statistics_page.dart';
import 'package:click_bazaar/features/nazoratchi/naz_profile/presentation/pages/naz_profile_page.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'core/widgets/custom_bottom_navigation_bar.dart';
import 'core/widgets/role_switcher.dart';
import 'features/nazoratchi/naz_statistics/presentation/pages/naz_statistics_page.dart';

class MainNazNavigationPage extends StatefulWidget {
  final Function(UserRole)? onRoleChanged;

  const MainNazNavigationPage({
    super.key,
    this.onRoleChanged,
  });

  @override
  State<MainNazNavigationPage> createState() => _MainNazNavigationPageState();
}

class _MainNazNavigationPageState extends State<MainNazNavigationPage> {
  int _currentIndex = 0;

  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = [
      const NazStatisticsPage(),
      const NazBozTuzilma(),
      NazProfilePage(onRoleChanged: widget.onRoleChanged ?? (role) {}),
    ];
  }

  final List<CustomBottomNavItem> _bottomNavItems = [
    const CustomBottomNavItem(
      iconPath: Assets.iconsHome,
      label: 'Asosiy',
      activeIconPath: Assets.iconsHome,
    ),
    const CustomBottomNavItem(
      iconPath: Assets.iconsMap,
      activeIconPath: Assets.iconsMap,
      label: 'Bozor tuzilmasi',
    ),
    const CustomBottomNavItem(
      label: 'Profil',
      iconPath: Assets.iconsProfile,
      activeIconPath: Assets.iconsProfile,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_currentIndex],
      bottomNavigationBar: CustomBottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: _bottomNavItems,
      ),
    );
  }
}
