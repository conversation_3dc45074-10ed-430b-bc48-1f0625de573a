import 'package:click_bazaar/core/widgets/custom_bottom_navigation_bar.dart';
import 'package:click_bazaar/core/widgets/role_switcher.dart';
import 'package:click_bazaar/features/sotuvchi/sot_home/presentation/page/sot_home_page.dart';
import 'package:click_bazaar/features/sotuvchi/sot_payment_history/presentation/page/sot_payment_history_page.dart';
import 'package:click_bazaar/features/sotuvchi/sot_empty_places/presentation/pages/sot_empty_places_page.dart';
import 'package:click_bazaar/features/sotuvchi/sot_profile/presentation/pages/sot_profile_page.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';

class MainSotNavigationPage extends StatefulWidget {
  final Function(UserRole)? onRoleChanged;

  const MainSotNavigationPage({
    super.key,
    this.onRoleChanged,
  });

  @override
  State<MainSotNavigationPage> createState() => _MainSotNavigationPageState();
}

class _MainSotNavigationPageState extends State<MainSotNavigationPage> {
  int _currentIndex = 0;

  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = [
      const SotHomePage(),
      const SotPaymentHistoryPage(),
      const SotEmptyPlacesPage(),
      SotProfilePage(onRoleChanged: widget.onRoleChanged),
    ];
  }

  final List<CustomBottomNavItem> _bottomNavItems = [
    const CustomBottomNavItem(
      iconPath: Assets.iconsHome,
      label: 'Asosiy',
      activeIconPath: Assets.iconsHome,
    ),
    const CustomBottomNavItem(
      iconPath: Assets.iconsPaymentHistory,
      activeIconPath: Assets.iconsPaymentHistory,
      label: 'To’lov tarixi',
    ),
    const CustomBottomNavItem(
      label: 'Bo\'sh joylar',
      iconPath: Assets.iconsFreeSpace,
      activeIconPath: Assets.iconsFreeSpace,
    ),
    const CustomBottomNavItem(
      label: 'Profil',
      iconPath: Assets.iconsProfile,
      activeIconPath: Assets.iconsProfile,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_currentIndex],
      bottomNavigationBar: CustomBottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: _bottomNavItems,
      ),
    );
  }
}
