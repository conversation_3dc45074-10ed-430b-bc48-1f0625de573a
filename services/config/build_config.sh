#!/bin/bash

# Build Configuration Management
# Handles loading and validation of build configuration

# =============================================================================
# CONFIGURATION VARIABLES
# =============================================================================

# Environment file paths
ENV_FILE="$PROJECT_ROOT/.env"
ENV_EXAMPLE_FILE="$SERVICES_DIR/config/.env.example"

# Default configuration values
DEFAULT_BUILD_TYPE="release"
DEFAULT_UPLOAD_TIMEOUT="300"
DEFAULT_MAX_FILE_SIZE="50000000"
DEFAULT_CHANGELOG_FILE="CHANGELOG.md"
DEFAULT_CHANGELOG_LINES="5"

# Configuration variables (will be loaded from .env)
TELEGRAM_BOT_TOKEN=""
TELEGRAM_CHAT_ID=""
TELEGRAM_TOPIC_ID=""
BUILD_TYPE_ENV=""
BUILD_FLAVOR_ENV=""
APP_NAME_OVERRIDE=""
GIT_BRANCH_OVERRIDE=""
UPLOAD_TIMEOUT=""
MAX_FILE_SIZE=""
FLUTTER_ROOT=""
FLUTTER_CHANNEL=""
DEBUG=""
ALWAYS_CLEAN=""
ALWAYS_TEST=""
CUSTOM_BUILD_ARGS=""
CHANGELOG_FILE=""
CHANGELOG_LINES=""
SUCCESS_NOTIFICATION_CHAT_ID=""
ERROR_NOTIFICATION_CHAT_ID=""

# =============================================================================
# CONFIGURATION LOADING
# =============================================================================

load_config() {
    log_info "Loading configuration..."
    
    # Check if .env file exists
    if [[ ! -f "$ENV_FILE" ]]; then
        log_warning ".env file not found. Creating from template..."
        create_env_from_template
        log_warning "Please configure your .env file and run the script again."
        exit 1
    fi
    
    # Load environment variables from .env file
    load_env_file
    
    # Validate required configuration
    validate_config
    
    # Set defaults for optional configuration
    set_config_defaults
    
    log_success "Configuration loaded successfully"
}

load_env_file() {
    # Source the .env file while handling potential issues
    set -a  # Automatically export all variables
    
    # Read .env file line by line to handle comments and empty lines
    while IFS= read -r line || [[ -n "$line" ]]; do
        # Skip empty lines and comments
        if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        # Handle lines with = sign
        if [[ "$line" =~ ^[[:space:]]*([^=]+)=(.*)$ ]]; then
            local key="${BASH_REMATCH[1]}"
            local value="${BASH_REMATCH[2]}"
            
            # Remove leading/trailing whitespace from key
            key=$(echo "$key" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
            
            # Remove quotes from value if present
            value=$(echo "$value" | sed 's/^["'\'']*//;s/["'\'']*$//')
            
            # Export the variable
            export "$key"="$value"
            
            # Truncate value for debug display
            local display_value="$value"
            if [[ ${#value} -gt 20 ]]; then
                display_value="${value:0:20}..."
            fi
            log_debug "Loaded config: $key=$display_value"
        fi
    done < "$ENV_FILE"
    
    set +a  # Stop automatically exporting variables
}

create_env_from_template() {
    if [[ -f "$ENV_EXAMPLE_FILE" ]]; then
        cp "$ENV_EXAMPLE_FILE" "$ENV_FILE"
        log_info "Created .env file from template"
    else
        log_error "Template file not found: $ENV_EXAMPLE_FILE"
        exit 1
    fi
}

# =============================================================================
# CONFIGURATION VALIDATION
# =============================================================================

validate_config() {
    local errors=0
    
    log_info "Validating configuration..."
    
    # Check required Telegram configuration
    if [[ -z "$TELEGRAM_BOT_TOKEN" || "$TELEGRAM_BOT_TOKEN" == "your_bot_token_here" ]]; then
        log_error "TELEGRAM_BOT_TOKEN is required in .env file"
        errors=$((errors + 1))
    fi
    
    if [[ -z "$TELEGRAM_CHAT_ID" || "$TELEGRAM_CHAT_ID" == "your_chat_id_here" ]]; then
        log_error "TELEGRAM_CHAT_ID is required in .env file"
        errors=$((errors + 1))
    fi
    
    # Validate build type
    if [[ -n "$BUILD_TYPE_ENV" && "$BUILD_TYPE_ENV" != "debug" && "$BUILD_TYPE_ENV" != "release" ]]; then
        log_error "BUILD_TYPE must be 'debug' or 'release'"
        errors=$((errors + 1))
    fi
    
    # Validate numeric values
    if [[ -n "$UPLOAD_TIMEOUT" && ! "$UPLOAD_TIMEOUT" =~ ^[0-9]+$ ]]; then
        log_error "UPLOAD_TIMEOUT must be a number"
        errors=$((errors + 1))
    fi
    
    if [[ -n "$MAX_FILE_SIZE" && ! "$MAX_FILE_SIZE" =~ ^[0-9]+$ ]]; then
        log_error "MAX_FILE_SIZE must be a number"
        errors=$((errors + 1))
    fi
    
    if [[ -n "$CHANGELOG_LINES" && ! "$CHANGELOG_LINES" =~ ^[0-9]+$ ]]; then
        log_error "CHANGELOG_LINES must be a number"
        errors=$((errors + 1))
    fi
    
    # Check Flutter configuration if specified
    if [[ -n "$FLUTTER_ROOT" && ! -d "$FLUTTER_ROOT" ]]; then
        log_error "FLUTTER_ROOT directory does not exist: $FLUTTER_ROOT"
        errors=$((errors + 1))
    fi
    
    if [[ "$errors" -gt 0 ]]; then
        log_error "Configuration validation failed with $errors error(s)"
        exit 1
    fi
    
    log_success "Configuration validation passed"
}

set_config_defaults() {
    # Set default values for optional configuration
    BUILD_TYPE_ENV="${BUILD_TYPE_ENV:-$DEFAULT_BUILD_TYPE}"
    UPLOAD_TIMEOUT="${UPLOAD_TIMEOUT:-$DEFAULT_UPLOAD_TIMEOUT}"
    MAX_FILE_SIZE="${MAX_FILE_SIZE:-$DEFAULT_MAX_FILE_SIZE}"
    CHANGELOG_FILE="${CHANGELOG_FILE:-$DEFAULT_CHANGELOG_FILE}"
    CHANGELOG_LINES="${CHANGELOG_LINES:-$DEFAULT_CHANGELOG_LINES}"
    
    # Set boolean defaults
    DEBUG="${DEBUG:-0}"
    ALWAYS_CLEAN="${ALWAYS_CLEAN:-0}"
    ALWAYS_TEST="${ALWAYS_TEST:-0}"
}

# =============================================================================
# PROJECT INFORMATION EXTRACTION
# =============================================================================

get_app_name() {
    if [[ -n "$APP_NAME_OVERRIDE" ]]; then
        echo "$APP_NAME_OVERRIDE"
        return
    fi
    
    # Extract app name from pubspec.yaml
    local app_name
    app_name=$(grep "^name:" "$PROJECT_ROOT/pubspec.yaml" | sed 's/name:[[:space:]]*//' | tr -d '"' | tr -d "'")
    
    if [[ -z "$app_name" ]]; then
        log_warning "Could not extract app name from pubspec.yaml"
        echo "flutter_app"
    else
        echo "$app_name"
    fi
}

get_app_version() {
    # Extract version from pubspec.yaml
    local version
    version=$(grep "^version:" "$PROJECT_ROOT/pubspec.yaml" | sed 's/version:[[:space:]]*//' | tr -d '"' | tr -d "'")
    
    if [[ -z "$version" ]]; then
        log_warning "Could not extract version from pubspec.yaml"
        echo "1.0.0"
    else
        echo "$version"
    fi
}

get_git_branch() {
    if [[ -n "$GIT_BRANCH_OVERRIDE" ]]; then
        echo "$GIT_BRANCH_OVERRIDE"
        return
    fi
    
    # Get current git branch
    local branch
    if command -v git &> /dev/null && [[ -d "$PROJECT_ROOT/.git" ]]; then
        branch=$(git -C "$PROJECT_ROOT" rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    else
        branch="unknown"
    fi
    
    echo "$branch"
}

get_git_commit_hash() {
    # Get current git commit hash (short)
    local commit_hash
    if command -v git &> /dev/null && [[ -d "$PROJECT_ROOT/.git" ]]; then
        commit_hash=$(git -C "$PROJECT_ROOT" rev-parse --short HEAD 2>/dev/null || echo "unknown")
    else
        commit_hash="unknown"
    fi
    
    echo "$commit_hash"
}

# =============================================================================
# BUILD CONFIGURATION HELPERS
# =============================================================================

get_build_flavor_args() {
    if [[ -n "$BUILD_FLAVOR" ]]; then
        echo "--flavor $BUILD_FLAVOR"
    fi
}

get_custom_build_args() {
    echo "$CUSTOM_BUILD_ARGS"
}

get_flutter_command() {
    if [[ -n "$FLUTTER_ROOT" ]]; then
        echo "$FLUTTER_ROOT/bin/flutter"
    else
        echo "flutter"
    fi
}

# =============================================================================
# CONFIGURATION DISPLAY
# =============================================================================

print_config_summary() {
    echo ""
    echo "📋 Configuration Summary"
    echo "========================"
    echo "App Name: $(get_app_name)"
    echo "Version: $(get_app_version)"
    echo "Build Type: $BUILD_TYPE"
    echo "Build Flavor: ${BUILD_FLAVOR:-"none"}"
    echo "Git Branch: $(get_git_branch)"
    echo "Platform: $PLATFORM"
    echo "Telegram Chat: ${TELEGRAM_CHAT_ID:0:20}..."
    echo "Upload Timeout: ${UPLOAD_TIMEOUT}s"
    echo "Max File Size: $(format_file_size "$MAX_FILE_SIZE")"
    echo ""
}
