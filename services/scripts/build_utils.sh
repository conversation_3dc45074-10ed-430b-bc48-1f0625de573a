#!/bin/bash

# Build Utilities for Flutter APK Building
# Handles Flutter build process, testing, and APK management

# =============================================================================
# BUILD PROCESS FUNCTIONS
# =============================================================================

simulate_build_process() {
    log_info "🧪 Simulating build process..."
    
    print_config_summary
    
    log_info "📋 Build steps that would be executed:"
    echo "  1. Validate Flutter environment"
    echo "  2. Check project structure"
    
    if [[ "$RUN_TESTS" == "true" ]]; then
        echo "  3. Run Flutter tests"
    fi
    
    if [[ "$CLEAN_BUILD" == "true" ]]; then
        echo "  4. Clean build directory"
    fi
    
    echo "  5. Build Flutter APK ($BUILD_TYPE)"
    
    if [[ -n "$BUILD_FLAVOR" ]]; then
        echo "     - With flavor: $BUILD_FLAVOR"
    fi
    
    echo "  6. Generate APK filename"
    echo "  7. Validate APK file"
    echo "  8. Prepare upload message"
    echo "  9. Upload to Telegram"
    
    local apk_name="$(get_app_name)_$(get_app_version)_${BUILD_TYPE}${BUILD_FLAVOR:+_$BUILD_FLAVOR}.apk"
    log_info "📱 APK would be named: $apk_name"
    
    log_success "🧪 Dry run completed successfully!"
}

execute_build_process() {
    log_info "🚀 Starting build process..."
    
    print_config_summary
    
    # Step 1: Validate Flutter environment
    validate_flutter_environment
    
    # Step 2: Check project structure
    validate_project_structure
    
    # Step 3: Run tests if requested
    if [[ "$RUN_TESTS" == "true" ]]; then
        run_flutter_tests
    fi
    
    # Step 4: Clean build if requested
    if [[ "$CLEAN_BUILD" == "true" ]]; then
        clean_flutter_build
    fi
    
    # Step 5: Build Flutter APK
    build_flutter_apk
    
    # Step 6: Validate and prepare APK
    local apk_path
    apk_path=$(find_built_apk)
    
    if [[ -z "$apk_path" || ! -f "$apk_path" ]]; then
        log_error "APK file not found after build"
        exit 1
    fi
    
    # Step 7: Rename APK with proper naming convention
    local final_apk_path
    final_apk_path=$(rename_apk "$apk_path")
    
    # Step 8: Validate APK size
    validate_apk_size "$final_apk_path"
    
    # Step 9: Upload to Telegram
    upload_apk_to_telegram "$final_apk_path"
    
    log_success "✅ Build process completed successfully!"
}

# =============================================================================
# FLUTTER ENVIRONMENT VALIDATION
# =============================================================================

validate_flutter_environment() {
    log_info "🔍 Validating Flutter environment..."
    
    local flutter_cmd
    flutter_cmd=$(get_flutter_command)
    
    # Check Flutter version
    log_info "Checking Flutter version..."
    if ! "$flutter_cmd" --version &> /dev/null; then
        log_fatal "Flutter command failed. Please check your Flutter installation and PATH. Suggestion: Run 'flutter doctor' and ensure Flutter is installed and in your PATH."
    fi
    
    local flutter_version
    flutter_version=$("$flutter_cmd" --version | head -n 1)
    log_info "Flutter version: $flutter_version"
    
    # Run Flutter doctor for detailed diagnostics
    log_info "Running Flutter doctor..."
    if [[ "$DEBUG_MODE" == "true" ]]; then
        "$flutter_cmd" doctor
    else
        "$flutter_cmd" doctor --android-licenses &> /dev/null || true
        "$flutter_cmd" doctor | grep -E "(✓|✗|!)" || true
    fi
    
    # Check if Android toolchain is available
    if ! "$flutter_cmd" doctor | grep -q "Android toolchain.*✓"; then
        log_warning "Android toolchain issues detected. Build may fail. Suggestion: Run 'flutter doctor' and resolve all issues."
    fi
    
    log_success "Flutter environment validation completed"
}

validate_project_structure() {
    log_info "📁 Validating project structure..."
    
    # Check essential files
    local required_files=("pubspec.yaml" "lib/main.dart" "android/app/build.gradle")
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$PROJECT_ROOT/$file" ]]; then
            log_fatal "Required file not found: $file. Suggestion: Ensure you are in the root of a valid Flutter project."
        fi
    done
    
    # Check if Android directory exists
    if [[ ! -d "$PROJECT_ROOT/android" ]]; then
        log_fatal "Android directory not found. This doesn't appear to be a Flutter project with Android support."
    fi
    
    # Validate build flavor if specified
    if [[ -n "$BUILD_FLAVOR" ]]; then
        validate_build_flavor
    fi
    
    log_success "Project structure validation completed"
}

validate_build_flavor() {
    log_info "🍃 Validating build flavor: $BUILD_FLAVOR"
    
    local build_gradle="$PROJECT_ROOT/android/app/build.gradle"
    
    # Check if flavor is defined in build.gradle
    if ! grep -q "productFlavors" "$build_gradle"; then
        log_warning "No productFlavors found in build.gradle, but flavor specified: $BUILD_FLAVOR"
        log_warning "Build may fail if flavor is not properly configured"
        return
    fi
    
    # Check if specific flavor exists
    if ! grep -A 20 "productFlavors" "$build_gradle" | grep -q "$BUILD_FLAVOR"; then
        log_warning "Flavor '$BUILD_FLAVOR' not found in build.gradle productFlavors"
        log_warning "Build may fail if flavor is not properly configured"
    else
        log_success "Build flavor '$BUILD_FLAVOR' found in configuration"
    fi
}

# =============================================================================
# TESTING FUNCTIONS
# =============================================================================

run_flutter_tests() {
    log_info "🧪 Running Flutter tests..."
    
    # Check if test directory exists and has test files
    if [[ ! -d "$PROJECT_ROOT/test" ]]; then
        log_warning "Test directory not found, skipping tests"
        return
    fi
    
    local test_files
    test_files=$(find "$PROJECT_ROOT/test" -name "*.dart" -type f | wc -l)
    
    if [[ "$test_files" -eq 0 ]]; then
        log_warning "No test files found in test directory, skipping tests"
        return
    fi
    
    log_info "Found $test_files test file(s), running tests..."
    
    local flutter_cmd
    flutter_cmd=$(get_flutter_command)
    
    # Run tests with timeout
    if timeout 300 "$flutter_cmd" test; then
        log_success "All tests passed!"
    else
        local exit_code=$?
        if [[ $exit_code -eq 124 ]]; then
            log_error "Tests timed out after 5 minutes"
        else
            log_error "Tests failed with exit code: $exit_code"
        fi
        exit 1
    fi
}

# =============================================================================
# BUILD FUNCTIONS
# =============================================================================

clean_flutter_build() {
    log_info "🧹 Cleaning Flutter build..."
    
    local flutter_cmd
    flutter_cmd=$(get_flutter_command)
    
    # Clean Flutter build
    "$flutter_cmd" clean
    
    # Remove build directories
    rm -rf "$PROJECT_ROOT/build" 2>/dev/null || true
    
    # Get dependencies
    "$flutter_cmd" pub get
    
    log_success "Build cleaned successfully"
}

build_flutter_apk() {
    log_info "🔨 Building Flutter APK..."
    
    local flutter_cmd
    flutter_cmd=$(get_flutter_command)
    
    # Prepare build command
    local build_cmd="$flutter_cmd build apk"
    
    # Add build type
    if [[ "$BUILD_TYPE" == "debug" ]]; then
        build_cmd="$build_cmd --debug"
    else
        build_cmd="$build_cmd --release"
    fi
    
    # Add flavor if specified
    local flavor_args
    flavor_args=$(get_build_flavor_args)
    if [[ -n "$flavor_args" ]]; then
        build_cmd="$build_cmd $flavor_args"
    fi
    
    # Add custom build arguments
    local custom_args
    custom_args=$(get_custom_build_args)
    if [[ -n "$custom_args" ]]; then
        build_cmd="$build_cmd $custom_args"
    fi
    
    log_info "Build command: $build_cmd"
    
    # Execute build with progress indication
    log_info "Building APK... This may take several minutes."
    
    if [[ "$DEBUG_MODE" == "true" ]]; then
        # Show full output in debug mode
        eval "$build_cmd"
    else
        # Show minimal output in normal mode
        eval "$build_cmd" 2>&1 | while IFS= read -r line; do
            if [[ "$line" =~ (Building|Running|Assembling|Built) ]]; then
                echo "  $line"
            fi
        done
    fi
    
    local build_exit_code=${PIPESTATUS[0]}
    
    if [[ $build_exit_code -ne 0 ]]; then
        log_fatal "Flutter build failed with exit code: $build_exit_code. Suggestion: Run 'flutter build apk --release' manually to see detailed errors."
    fi
    
    log_success "APK build completed successfully!"
}

# =============================================================================
# APK MANAGEMENT FUNCTIONS
# =============================================================================

find_built_apk() {
    local apk_path=""
    
    # Look for APK in different possible locations
    local search_paths=(
        "$PROJECT_ROOT/build/app/outputs/flutter-apk"
        "$PROJECT_ROOT/build/app/outputs/apk"
        "$PROJECT_ROOT/build/app/outputs/apk/$BUILD_TYPE"
    )
    
    if [[ -n "$BUILD_FLAVOR" ]]; then
        search_paths+=(
            "$PROJECT_ROOT/build/app/outputs/apk/$BUILD_FLAVOR/$BUILD_TYPE"
            "$PROJECT_ROOT/build/app/outputs/flutter-apk/$BUILD_FLAVOR-$BUILD_TYPE"
        )
    fi
    
    for path in "${search_paths[@]}"; do
        if [[ -d "$path" ]]; then
            local found_apk
            found_apk=$(find "$path" -name "*.apk" -type f | head -n 1)
            if [[ -n "$found_apk" ]]; then
                apk_path="$found_apk"
                break
            fi
        fi
    done
    
    echo "$apk_path"
}

rename_apk() {
    local original_apk="$1"
    local app_name
    local app_version
    local new_filename
    
    app_name=$(get_app_name)
    app_version=$(get_app_version)
    
    # Generate new filename
    new_filename="${app_name}_${app_version}_${BUILD_TYPE}"
    if [[ -n "$BUILD_FLAVOR" ]]; then
        new_filename="${new_filename}_${BUILD_FLAVOR}"
    fi
    new_filename="${new_filename}.apk"
    
    # Clean filename (remove spaces and special characters)
    new_filename=$(echo "$new_filename" | tr ' ' '_' | tr -cd '[:alnum:]._-')
    
    local new_path="$(dirname "$original_apk")/$new_filename"
    
    # Rename the file
    if [[ "$original_apk" != "$new_path" ]]; then
        mv "$original_apk" "$new_path"
        log_info "📱 APK renamed to: $new_filename"
    fi
    
    echo "$new_path"
}

validate_apk_size() {
    local apk_path="$1"
    local file_size
    
    file_size=$(get_file_size "$apk_path")
    
    log_info "📦 APK size: $(format_file_size "$file_size")"
    
    # Check if file size exceeds Telegram limit
    if [[ "$file_size" -gt "$MAX_FILE_SIZE" ]]; then
        log_error "APK size ($(format_file_size "$file_size")) exceeds maximum allowed size ($(format_file_size "$MAX_FILE_SIZE"))"
        log_error "Consider building a debug APK or reducing app size"
        exit 1
    fi
    
    log_success "APK size validation passed"
}
