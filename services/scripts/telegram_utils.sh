#!/bin/bash

# Telegram Utilities for APK Upload
# Handles Telegram Bot API interactions for file uploads and messaging

# =============================================================================
# TELEGRAM API CONFIGURATION
# =============================================================================

TELEGRAM_API_BASE="https://api.telegram.org/bot"
TELEGRAM_FILE_API_BASE="https://api.telegram.org/file/bot"
TELEGRAM_MAX_FILE_SIZE=52428800  # 50MB in bytes
TELEGRAM_MAX_MESSAGE_LENGTH=4096

# =============================================================================
# TELEGRAM API FUNCTIONS
# =============================================================================

# Send a text message to Telegram
send_telegram_message() {
    local message="$1"
    local chat_id="${2:-$TELEGRAM_CHAT_ID}"
    local topic_id="${3:-$TELEGRAM_TOPIC_ID}"
    
    if [[ -z "$TELEGRAM_BOT_TOKEN" || -z "$chat_id" ]]; then
        log_error "Telegram bot token or chat ID not configured"
        return 1
    fi
    
    local url="${TELEGRAM_API_BASE}${TELEGRAM_BOT_TOKEN}/sendMessage"
    local data="chat_id=${chat_id}&text=${message}&parse_mode=Markdown"
    
    # Add topic ID if specified (for groups with topics)
    if [[ -n "$topic_id" ]]; then
        data="${data}&message_thread_id=${topic_id}"
    fi
    
    log_debug "Sending message to Telegram: ${message:0:100}..."
    
    local response
    if command_exists curl; then
        response=$(curl -s -X POST "$url" -d "$data")
    else
        log_error "curl is required for Telegram API calls"
        return 1
    fi
    
    # Check if message was sent successfully
    if echo "$response" | grep -q '"ok":true'; then
        log_debug "Message sent successfully"
        return 0
    else
        log_error "Failed to send Telegram message: $response"
        return 1
    fi
}

# Upload file to Telegram
upload_file_to_telegram() {
    local file_path="$1"
    local caption="$2"
    local chat_id="${3:-$TELEGRAM_CHAT_ID}"
    local topic_id="${4:-$TELEGRAM_TOPIC_ID}"
    
    if [[ ! -f "$file_path" ]]; then
        log_error "File not found: $file_path"
        return 1
    fi
    
    if [[ -z "$TELEGRAM_BOT_TOKEN" || -z "$chat_id" ]]; then
        log_error "Telegram bot token or chat ID not configured"
        return 1
    fi
    
    local file_size
    file_size=$(get_file_size "$file_path")
    
    if [[ "$file_size" -gt "$TELEGRAM_MAX_FILE_SIZE" ]]; then
        log_error "File size ($(format_file_size "$file_size")) exceeds Telegram limit ($(format_file_size "$TELEGRAM_MAX_FILE_SIZE"))"
        return 1
    fi
    
    local url="${TELEGRAM_API_BASE}${TELEGRAM_BOT_TOKEN}/sendDocument"
    
    log_info "📤 Uploading file to Telegram..."
    log_info "File: $(basename "$file_path")"
    log_info "Size: $(format_file_size "$file_size")"
    
    # Prepare curl command
    local curl_cmd="curl -s -X POST \"$url\""
    curl_cmd="$curl_cmd -F \"chat_id=$chat_id\""
    curl_cmd="$curl_cmd -F \"document=@$file_path\""
    
    if [[ -n "$caption" ]]; then
        # Truncate caption if too long
        if [[ ${#caption} -gt $TELEGRAM_MAX_MESSAGE_LENGTH ]]; then
            caption="${caption:0:$((TELEGRAM_MAX_MESSAGE_LENGTH-3))}..."
        fi
        curl_cmd="$curl_cmd -F \"caption=$caption\""
        curl_cmd="$curl_cmd -F \"parse_mode=Markdown\""
    fi
    
    # Add topic ID if specified
    if [[ -n "$topic_id" ]]; then
        curl_cmd="$curl_cmd -F \"message_thread_id=$topic_id\""
    fi
    
    # Execute upload with timeout
    local response
    if ! response=$(timeout "$UPLOAD_TIMEOUT" bash -c "$curl_cmd"); then
        local exit_code=$?
        if [[ $exit_code -eq 124 ]]; then
            log_error "Upload timed out after ${UPLOAD_TIMEOUT} seconds"
        else
            log_error "Upload failed with exit code: $exit_code"
        fi
        return 1
    fi
    
    # Check if upload was successful
    if echo "$response" | grep -q '"ok":true'; then
        log_success "File uploaded successfully to Telegram!"
        return 0
    else
        log_error "Failed to upload file to Telegram: $response"
        return 1
    fi
}

# =============================================================================
# MESSAGE FORMATTING FUNCTIONS
# =============================================================================

# Generate upload message for APK
generate_apk_upload_message() {
    local apk_path="$1"
    local app_name
    local app_version
    local file_size
    local git_branch
    local git_commit
    local timestamp
    local changelog
    
    app_name=$(get_app_name)
    app_version=$(get_app_version)
    file_size=$(get_file_size "$apk_path")
    git_branch=$(get_git_branch)
    git_commit=$(get_git_commit_hash)
    timestamp=$(get_timestamp)
    changelog=$(get_changelog_content)
    
    local message=""
    message+="📱 **${app_name} v${app_version}**\n"
    message+="📦 **Size:** $(format_file_size "$file_size")\n"
    message+="🌿 **Branch:** \`${git_branch}\`\n"
    
    if [[ "$git_commit" != "unknown" ]]; then
        message+="🔗 **Commit:** \`${git_commit}\`\n"
    fi
    
    if [[ -n "$changelog" ]]; then
        message+="📝 **Changelog:**\n${changelog}\n"
    fi
    
    message+="🔧 **Build:** ${BUILD_TYPE}"
    if [[ -n "$BUILD_FLAVOR" ]]; then
        message+=" (${BUILD_FLAVOR})"
    fi
    message+="\n"
    
    message+="⏰ **Built:** ${timestamp}"
    
    echo -e "$message"
}

# Get changelog content
get_changelog_content() {
    local changelog_file="$PROJECT_ROOT/$CHANGELOG_FILE"
    local changelog=""
    
    if [[ -f "$changelog_file" ]]; then
        # Extract recent changelog entries
        changelog=$(head -n "$CHANGELOG_LINES" "$changelog_file" | sed 's/^/- /')
    elif [[ -f "$PROJECT_ROOT/CHANGELOG.md" ]]; then
        # Fallback to CHANGELOG.md
        changelog=$(head -n "$CHANGELOG_LINES" "$PROJECT_ROOT/CHANGELOG.md" | sed 's/^/- /')
    else
        # Generate changelog from git commits if available
        if command -v git &> /dev/null && [[ -d "$PROJECT_ROOT/.git" ]]; then
            changelog=$(git -C "$PROJECT_ROOT" log --oneline -n "$CHANGELOG_LINES" 2>/dev/null | sed 's/^[a-f0-9]* /- /' || echo "")
        fi
    fi
    
    # Clean up changelog
    if [[ -n "$changelog" ]]; then
        # Remove empty lines and limit length
        changelog=$(echo "$changelog" | grep -v '^[[:space:]]*$' | head -n "$CHANGELOG_LINES")
    fi
    
    echo "$changelog"
}

# =============================================================================
# TELEGRAM VALIDATION FUNCTIONS
# =============================================================================

# Test Telegram connection
test_telegram_connection() {
    log_info "🔍 Testing Telegram connection..."
    
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]]; then
        log_error "TELEGRAM_BOT_TOKEN not configured"
        return 1
    fi
    
    local url="${TELEGRAM_API_BASE}${TELEGRAM_BOT_TOKEN}/getMe"
    local response
    
    if command_exists curl; then
        response=$(curl -s "$url")
    else
        log_error "curl is required for Telegram API calls"
        return 1
    fi
    
    if echo "$response" | grep -q '"ok":true'; then
        local bot_name
        bot_name=$(echo "$response" | grep -o '"first_name":"[^"]*"' | cut -d'"' -f4)
        log_success "Connected to Telegram bot: ${bot_name:-"Unknown"}"
        return 0
    else
        log_error "Failed to connect to Telegram bot: $response"
        return 1
    fi
}

# Validate chat access
validate_telegram_chat() {
    log_info "🔍 Validating Telegram chat access..."
    
    if [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        log_error "TELEGRAM_CHAT_ID not configured"
        return 1
    fi
    
    # Try to send a test message
    local test_message="🤖 Build service connection test"
    
    if send_telegram_message "$test_message"; then
        log_success "Telegram chat access validated"
        return 0
    else
        log_error "Failed to access Telegram chat. Check your TELEGRAM_CHAT_ID and bot permissions."
        return 1
    fi
}

# Fetch and print Telegram chat/channel and topic name
get_telegram_chat_info() {
    if [[ -z "$TELEGRAM_BOT_TOKEN" || -z "$TELEGRAM_CHAT_ID" ]]; then
        log_warning "Cannot fetch chat info: TELEGRAM_BOT_TOKEN or TELEGRAM_CHAT_ID not set."
        return
    fi
    local url="${TELEGRAM_API_BASE}${TELEGRAM_BOT_TOKEN}/getChat?chat_id=${TELEGRAM_CHAT_ID}"
    local response
    if command_exists curl; then
        response=$(curl -s "$url")
    else
        log_warning "curl is required to fetch chat info."
        return
    fi
    local chat_title chat_type
    chat_title=$(echo "$response" | grep -o '"title":"[^"]*"' | head -1 | cut -d'"' -f4)
    chat_type=$(echo "$response" | grep -o '"type":"[^"]*"' | head -1 | cut -d'"' -f4)
    if [[ -n "$chat_title" ]]; then
        log_info "Resolved Telegram chat name: $chat_title ($chat_type)"
    else
        log_warning "Could not resolve chat name. API response: $response"
    fi
    # If topic ID is set, try to fetch topic name
    if [[ -n "$TELEGRAM_TOPIC_ID" ]]; then
        local topic_url="${TELEGRAM_API_BASE}${TELEGRAM_BOT_TOKEN}/getForumTopic?chat_id=${TELEGRAM_CHAT_ID}&message_thread_id=${TELEGRAM_TOPIC_ID}"
        local topic_response
        topic_response=$(curl -s "$topic_url")
        local topic_name
        topic_name=$(echo "$topic_response" | grep -o '"name":"[^"]*"' | head -1 | cut -d'"' -f4)
        if [[ -n "$topic_name" ]]; then
            log_info "Resolved Telegram topic name: $topic_name"
        else
            log_warning "Could not resolve topic name. API response: $topic_response"
        fi
    fi
}

# =============================================================================
# MAIN UPLOAD FUNCTION
# =============================================================================

# Upload APK to Telegram with formatted message
upload_apk_to_telegram() {
    local apk_path="$1"
    
    if [[ ! -f "$apk_path" ]]; then
        log_error "APK file not found: $apk_path"
        return 1
    fi

    # Print resolved Telegram target
    local chat_type=""
    if [[ "$TELEGRAM_CHAT_ID" =~ ^-100 ]]; then
        chat_type="(channel or supergroup)"
    elif [[ "$TELEGRAM_CHAT_ID" =~ ^- ]]; then
        chat_type="(group)"
    else
        chat_type="(private chat)"
    fi
    log_info "Will post to Telegram $chat_type: $TELEGRAM_CHAT_ID${TELEGRAM_TOPIC_ID:+, topic: $TELEGRAM_TOPIC_ID}"
    get_telegram_chat_info

    # Test connection first
    if ! test_telegram_connection; then
        return 1
    fi
    
    # Generate upload message
    local message
    message=$(generate_apk_upload_message "$apk_path")
    
    log_info "📤 Uploading APK to Telegram..."
    log_debug "Upload message: $message"
    
    # Upload file with message
    if upload_file_to_telegram "$apk_path" "$message"; then
        log_success "🎉 APK uploaded successfully to Telegram!"
        
        # Send success notification to different chat if configured
        if [[ -n "$SUCCESS_NOTIFICATION_CHAT_ID" && "$SUCCESS_NOTIFICATION_CHAT_ID" != "$TELEGRAM_CHAT_ID" ]]; then
            local success_msg="✅ Build completed successfully for $(get_app_name) v$(get_app_version)"
            send_telegram_message "$success_msg" "$SUCCESS_NOTIFICATION_CHAT_ID"
        fi
        
        return 0
    else
        log_error "Failed to upload APK to Telegram"
        
        # Send error notification to different chat if configured
        if [[ -n "$ERROR_NOTIFICATION_CHAT_ID" ]]; then
            local error_msg="❌ Build failed for $(get_app_name) v$(get_app_version)"
            send_telegram_message "$error_msg" "$ERROR_NOTIFICATION_CHAT_ID"
        fi
        
        return 1
    fi
}
